<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_no_start_spell_group_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="@dimen/dimen_dp_60"
    tools:visibility="visible"
    android:visibility="gone">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_goods_detail_no_start_spell_group2"
        android:paddingStart="10dp" >

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:text="拼团价"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_no_start_spell_group_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@+id/tv_title"
            app:layout_constraintBottom_toBottomOf="@+id/tv_title"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginStart="@dimen/dimen_dp_3"
            android:background="@drawable/shape_spell_group_price"
            tools:text="¥15.25" />

        <!-- 单价显示（第二行） -->
        <TextView
            android:id="@+id/tv_no_start_unit_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="@+id/tv_title"
            android:textSize="11sp"
            android:textColor="@color/white"
            android:background="@color/color_F5F5F5"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="@dimen/dimen_dp_8"
            tools:text="单价"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_no_start_spell_group_time"
            android:layout_width="170dp"
            android:layout_height="@dimen/dimen_dp_24"
            android:gravity="center_vertical"
            android:layout_marginStart="@dimen/dimen_dp_7"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_no_start_spell_group_price"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            android:textColor="#FF9C4600"
            android:textSize="12sp"
            android:paddingStart="@dimen/dimen_dp_10"
            android:background="@drawable/shape_no_start_time_bg"
            tools:text="03月21日 18:00 开抢" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>