<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_spell_group_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="60dp"
    android:visibility="gone"
    tools:visibility="visible">

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/bg_detail_time_layout"
        android:gravity="center_vertical"
        android:paddingStart="10dp">

        <!-- 创建一个容器来包含拼团价和进度信息，用于垂直居中对齐 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerVertical="true">

            <!--拼团价-->
            <LinearLayout
                android:id="@+id/ll_spell_group_01"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:baselineAligned="true"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_4"
                    android:text="拼团价"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:gravity="center_vertical"
                    android:layout_gravity="center_vertical" />

                <TextView
                    android:id="@+id/tv_spell_group_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:gravity="center_vertical"
                    android:layout_gravity="center_vertical"
                    tools:text="¥15.25" />

                <TextView
                    android:id="@+id/tv_spell_group_original_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:drawableRight="@drawable/icon_product_detail_tips"
                    android:drawablePadding="3dp"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="gone"
                    android:gravity="center_vertical"
                    android:layout_gravity="center_vertical"
                    tools:text="¥10.25"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- 第二行：单价和拼团进度 -->
            <LinearLayout
                android:id="@+id/ll_second_line_simple"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp"
                android:visibility="gone">

                <!-- 单价显示 -->
                <TextView
                    android:id="@+id/tv_unit_price_simple"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="11dp"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    tools:text="单价"
                    tools:visibility="visible" />

                <!-- 分隔线 -->
                <View
                    android:id="@+id/view_divider_simple"
                    android:layout_width="1dp"
                    android:layout_height="12dp"
                    android:background="@color/white"
                    android:visibility="gone"
                    android:layout_marginStart="6dp" />

                <!--拼团进度-->
                <LinearLayout
                    android:id="@+id/rl_spell_group_02"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginStart="6dp">

                    <ProgressBar
                        android:id="@+id/progress"
                        style="@style/HorizontalProgressBarSpellGroup"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:minWidth="@dimen/dimen_dp_110"
                        android:progress="60"
                        tools:progress="60" />

                    <TextView
                        android:id="@+id/tv_spell_group_already"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        tools:text="已拼400盒" />

                    <TextView
                        android:id="@+id/tv_spell_group_initial"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        tools:text="20盒起拼" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@color/color_4dff982c"
        android:gravity="center"
        android:minWidth="@dimen/dimen_dp_115"
        android:orientation="vertical"
        android:paddingLeft="12dp"
        android:paddingRight="12dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_spell_group_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="距结束仅剩"
                android:textColor="@color/color_292933"
                android:textSize="10sp" />

            <LinearLayout
                android:id="@+id/ll_spell_group_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!--                <TextView-->
                <!--                    android:id="@+id/tv_spell_group_day"-->
                <!--                    style="@style/home_seckill_style_02"-->
                <!--                    android:text="12" />-->

                <!--                <TextView-->
                <!--                    android:id="@+id/tv_spell_group_dot_day"-->
                <!--                    style="@style/home_seckill_point_02"-->
                <!--                    android:text=":" />-->

                <TextView
                    android:id="@+id/tv_spell_group_hour"
                    style="@style/home_seckill_style_02"
                    android:text="12" />

                <TextView
                    style="@style/home_seckill_point_02"
                    android:text=":" />

                <TextView
                    android:id="@+id/tv_spell_group_minute"
                    style="@style/home_seckill_style_02"
                    android:text="50" />

                <TextView
                    style="@style/home_seckill_point_02"
                    android:text=":" />

                <TextView
                    android:id="@+id/tv_spell_group_second"
                    style="@style/home_seckill_style_02"
                    android:text="50" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>