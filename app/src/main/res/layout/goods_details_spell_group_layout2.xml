<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_spell_group_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:visibility="gone"
    android:orientation="horizontal"
    tools:visibility="visible">

    <!--拼团价-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_spell_group_01"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_weight="1"
        android:layout_height="@dimen/dimen_dp_60"
        android:visibility="gone"
        tools:visibility="visible"
        android:background="@drawable/bg_detail_time_layout2" >

        <ImageView
            android:layout_width="42dp"
            android:layout_height="@dimen/dimen_dp_54"
            android:src="@drawable/icon_sepell_group_fire"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="@dimen/dimen_dp_19" />


        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:text="拼团价"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_spell_group_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/tv_title"
            app:layout_constraintStart_toEndOf="@+id/tv_title"
            android:layout_marginStart="@dimen/dimen_dp_3"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/shape_spell_group_price"
            tools:text="¥15.25" />

        <TextView
            android:id="@+id/tv_spell_group_original_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_5"
            android:background="@drawable/shape_spell_group_origin_price"
            android:textColor="@color/white"
            android:drawableRight="@drawable/icon_product_detail_tips"
            android:drawablePadding="3dp"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tv_spell_group_price"
            app:layout_constraintStart_toEndOf="@+id/tv_spell_group_price"
            tools:text="¥10.25"
            tools:visibility="visible" />

        <!-- 单价显示（第二行） -->
        <TextView
            android:id="@+id/tv_unit_price_simple"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="@+id/tv_title"
            android:textSize="@dimen/dimen_dp_11"
            android:textColor="@color/white"
            android:background="@color/color_F5F5F5"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="@dimen/dimen_dp_8"
            tools:text="单价"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_spell_group_aptitude"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_18"
            android:text="价格认证资质可见"
            android:visibility="gone"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_15" />

        <TextView
            android:id="@+id/tv_spell_group_control"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_18"
            android:text="价格签署协议可见"
            android:visibility="gone"
            tools:visibility="gone"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_15" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_spell_group_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:constraint_referenced_ids="tv_title, tv_spell_group_price" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_spell_group_02"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_weight="1"
        android:layout_height="@dimen/dimen_dp_60"
        tools:visibility="gone"
        android:background="@drawable/bg_detail_time_layout2" >

        <ImageView
            android:layout_width="42dp"
            android:layout_height="@dimen/dimen_dp_54"
            android:src="@drawable/icon_sepell_group_fire"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="@dimen/dimen_dp_19" />


        <TextView
            android:id="@+id/tv_title_gross"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/dimen_dp_15"
            android:layout_marginStart="@dimen/dimen_dp_18"
            android:text="拼团价"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_12" />

        <!-- 单价显示 -->
        <TextView
            android:id="@+id/tv_unit_price_bottom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="@+id/tv_title_gross"
            android:textSize="@dimen/dimen_dp_11"
            android:textColor="@color/white"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="@dimen/dimen_dp_8"
            tools:text="单价"
            tools:visibility="visible" />

        <!-- 分隔线 -->
        <View
            android:id="@+id/view_divider1"
            android:layout_width="1dp"
            android:layout_height="12dp"
            android:background="@color/white"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@+id/tv_unit_price_bottom"
            app:layout_constraintBottom_toBottomOf="@+id/tv_unit_price_bottom"
            app:layout_constraintTop_toTopOf="@+id/tv_unit_price_bottom"
            android:layout_marginStart="6dp" />

        <TextView
            android:id="@+id/tv_spell_group_gross_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@+id/view_divider1"
            android:textSize="@dimen/dimen_dp_11"
            android:textColor="@color/white"
            tools:text="建议零售价 ¥19.00（终端毛利率25%）"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="@dimen/dimen_dp_8"
            android:layout_marginStart="6dp" />

        <TextView
            android:id="@+id/tv_spell_group_price_gross"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/tv_title_gross"
            app:layout_constraintStart_toEndOf="@+id/tv_title_gross"
            android:layout_marginStart="@dimen/dimen_dp_3"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/shape_spell_group_price"
            tools:text="¥15.25" />

        <TextView
            android:id="@+id/tv_spell_group_price_gross_origin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@+id/tv_spell_group_price_gross"
            app:layout_constraintBottom_toBottomOf="@+id/tv_spell_group_price_gross"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="gone"
            tools:text="¥10.25"
            android:layout_marginStart="@dimen/dimen_dp_3"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_spell_group_aptitude_gross"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_18"
            android:text="价格认证资质可见"
            android:visibility="gone"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_15" />

        <TextView
            android:id="@+id/tv_spell_group_control_gross"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_18"
            android:text="价格签署协议可见"
            android:visibility="gone"
            tools:visibility="gone"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_15" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_spell_group_price_gross"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:constraint_referenced_ids="tv_title_gross, tv_spell_group_price_gross" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/lySpellGroupTimeContent"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_60"
        android:background="@color/color_4dff982c"
        android:gravity="center"
        android:minWidth="@dimen/dimen_dp_115"
        android:orientation="vertical"
        android:paddingLeft="12dp"
        android:visibility="visible"
        android:paddingRight="12dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_spell_group_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="距结束仅剩"
                android:textColor="@color/color_292933"
                android:textSize="10sp" />

            <LinearLayout
                android:id="@+id/ll_spell_group_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!--                <TextView-->
                <!--                    android:id="@+id/tv_spell_group_day"-->
                <!--                    style="@style/home_seckill_style_02"-->
                <!--                    android:text="12" />-->

                <!--                <TextView-->
                <!--                    android:id="@+id/tv_spell_group_dot_day"-->
                <!--                    style="@style/home_seckill_point_02"-->
                <!--                    android:text=":" />-->

                <TextView
                    android:id="@+id/tv_spell_group_hour"
                    style="@style/home_seckill_style_02"
                    android:text="12" />

                <TextView
                    style="@style/home_seckill_point_02"
                    android:text=":" />

                <TextView
                    android:id="@+id/tv_spell_group_minute"
                    style="@style/home_seckill_style_02"
                    android:text="50" />

                <TextView
                    style="@style/home_seckill_point_02"
                    android:text=":" />

                <TextView
                    android:id="@+id/tv_spell_group_second"
                    style="@style/home_seckill_style_02"
                    android:text="50" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>