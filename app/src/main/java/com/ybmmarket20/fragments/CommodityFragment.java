package com.ybmmarket20.fragments;

import static android.app.Activity.RESULT_OK;
import static com.ybmmarket20.constant.IntentCanst.JG_ENTRANCE;
import static com.ybmmarket20.constant.IntentCanst.JG_REFERRER;
import static com.ybmmarket20.constant.IntentCanst.JG_REFERRER_TITLE;
import static com.ybmmarket20.constant.IntentCanst.JG_TRACK_BEAN;
import static com.ybmmarket20.constant.IntentCanst.MODULE;
import static com.ybmmarket20.constant.IntentCanst.OFFSET;
import static com.ybmmarket20.constant.IntentCanst.PAGE_ID;
import static com.ybmmarket20.constant.IntentCanst.SHOW_ADDITIONALPURCHASE_FLAG;
import static com.ybmmarket20.constant.IntentCanst.SHOW_GROUPPURCHASE_FLAG;
import static com.ybmmarket20.constant.IntentCanst.SOURCETYPE;
import static com.ybmmarket20.utils.analysis.AnalysisConst.Cart.PAGE_COMMODITYDETAILS_GIFTCARD_CLICK;
import static com.ybmmarket20.utils.analysis.AnalysisConst.Cart.PAGE_COMMODITYDETAILS_GIFTCARD_EXPOSURE;
import static com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModelKt.SPELL_GROUP_RECOMMEND_DEFAULT;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Message;
import android.os.Parcelable;
import android.text.ClipboardManager;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.arch.core.executor.ArchTaskExecutor;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.flyco.tablayout.SlidingTabLayout;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.luck.picture.lib.tools.ScreenUtils;
import com.xyy.canary.utils.DensityUtil;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.AptitudeActivity;
import com.ybmmarket20.activity.DepreciateInformActivity;
import com.ybmmarket20.activity.ProductDetailActivity;
import com.ybmmarket20.activity.ShareCallback;
import com.ybmmarket20.adapter.DetailServiceAdapter;
import com.ybmmarket20.adapter.MarqueeViewAdapter;
import com.ybmmarket20.adapter.PriceRangeAdapter;
import com.ybmmarket20.adapter.ProductDetailCnMedineAttrAdapter;
import com.ybmmarket20.bean.ActPtBean;
import com.ybmmarket20.bean.AssembleOrderList;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.GroupPurchaseInfo;
import com.ybmmarket20.bean.GroupPurchaseInfoKt;
import com.ybmmarket20.bean.ImPackUrlBean;
import com.ybmmarket20.bean.ImagesVideosListBean;
import com.ybmmarket20.bean.JgRequestParams;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.bean.NextDayServiceTagListBean;
import com.ybmmarket20.bean.PopMerchantsBean;
import com.ybmmarket20.bean.PriceRangeListBean;
import com.ybmmarket20.bean.ProductDetailBean;
import com.ybmmarket20.bean.ProductDetailBeanWrapper;
import com.ybmmarket20.bean.ProductDetailImageBean;
import com.ybmmarket20.bean.ProductDetailKeyAttr;
import com.ybmmarket20.bean.ProductDetailPriceAfterDiscountBean;
import com.ybmmarket20.bean.ProductInstructionBean;
import com.ybmmarket20.bean.PromiseListBean;
import com.ybmmarket20.bean.RangePriceBeanKt;
import com.ybmmarket20.bean.RefreshWrapperPagerBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.RowsBeanCombinedExt;
import com.ybmmarket20.bean.RowsListBean;
import com.ybmmarket20.bean.SeckillBean;
import com.ybmmarket20.bean.TagBean;
import com.ybmmarket20.bean.product_detail.ReportPDButtonClick;
import com.ybmmarket20.bean.product_detail.ReportPDExposure;
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean;
import com.ybmmarket20.bean.promotion.SkuDetailPromotionInfo;
import com.ybmmarket20.business.correction.ui.activity.MainCorrectionActivity;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.JgOperationPositionInfo;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.TrackManager;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.eventbus.C;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.eventbus.EventBusUtil;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.widget.RoundTextView;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.ConstantData;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.db.info.HandlerGoodsDao;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.report.coupon.CouponEntryType;
import com.ybmmarket20.report.coupon.ICouponEntryType;
import com.ybmmarket20.reportBean.AddToCart;
import com.ybmmarket20.reportBean.JGPageListCommonBean;
import com.ybmmarket20.utils.AdapterUtils;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.DateTimeUtil;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.AnalysisConst;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.CSUListAdapter;
import com.ybmmarket20.view.CSUListView;
import com.ybmmarket20.view.CommodityBannerLayout;
import com.ybmmarket20.view.CommodityRecommendLayout;
import com.ybmmarket20.view.CommodityRecyclerLayout;
import com.ybmmarket20.view.CommonDialogLayout;
import com.ybmmarket20.view.DetailOperationToolRecommendGoodsView;
import com.ybmmarket20.view.GlideRoundTransform;
import com.ybmmarket20.view.ImageLayout;
import com.ybmmarket20.view.MarqueeViewSpellGroup;
import com.ybmmarket20.view.MyGridView;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.PageBehavior;
import com.ybmmarket20.view.ProductDetailCnmExtLayout;
import com.ybmmarket20.view.ProductDetailControlView;
import com.ybmmarket20.view.ProductDetailPopWindow2;
import com.ybmmarket20.view.ProductDiscountPopWindow;
import com.ybmmarket20.view.ProductEditLayout;
import com.ybmmarket20.view.ProductEditLayout4;
import com.ybmmarket20.view.ShopNameWithTagView;
import com.ybmmarket20.view.ShowPromotionPopWindowNew;
import com.ybmmarket20.view.ShowSpellGroupPopWindow;
import com.ybmmarket20.view.ViewPagerSlide;
import com.ybmmarket20.view.homesteady.HomeSteadySpellGroupView;
import com.ybmmarket20.view.homesteady.callback.ISpellGroupAnalysisCallback;
import com.ybmmarket20.view.sameSpecifications.SameSpecificationsListView;
import com.ybmmarket20.xyyreport.page.commodity.CommodityDetailReport;
import com.ybmmarket20.xyyreport.page.search.GoodsPlaceExposureRecord;
import com.ybmmarket20.xyyreport.page.search.SearchProductReport;
import com.ybmmarketkotlin.adapter.ComboPagerAdapter;
import com.ybmmarketkotlin.adapter.GoodListAdapterNew;
import com.ybmmarketkotlin.adapter.GoodsListAdapterNewCategory;
import com.ybmmarketkotlin.bean.CmsPageParam;
import com.ybmmarketkotlin.bean.CommodityComboBean;
import com.ybmmarketkotlin.bean.CommodityGroupRecommondBean;
import com.ybmmarketkotlin.fragments.ComboFragment;
import com.ybmmarketkotlin.utils.TextViewKt;
import com.ybmmarketkotlin.viewmodel.CommodityViewModel;
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener;
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyMultiLayout;
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuySingleLayout;
import com.ydmmarket.report.ReportManager;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function0;

/**
 * 商品详情
 */
public class CommodityFragment extends CommodityAnalysisFragment implements PageBehavior.OnPageChanged, ICouponEntryType {

    @Bind(R.id.brand_iv)
    CommodityBannerLayout brandIv;
    @Bind(R.id.iv_brand_mark)
    ImageView ivBrandMark;
    @Bind(R.id.tv_activity_price)
    TextView tvActivityPrice;
    @Bind(R.id.tv_sold_out)
    TextView tvSoldOut;
    @Bind(R.id.tv_tax_amount_time)
    TextView tvTaxAmountTime;
    @Bind(R.id.tv_tax_discount_price)
    TextView tvTaxDiscountPrice;
    @Bind(R.id.tv_time)
    TextView tvTime;
    @Bind(R.id.tv_day)
    TextView tvDay;
    @Bind(R.id.tv_dot_day)
    TextView tvDotDay;
    @Bind(R.id.tv_dot_hour)
    TextView tvDotHour;
    @Bind(R.id.tv_hour)
    TextView tvHour;
    @Bind(R.id.tv_minute)
    TextView tvMinute;
    @Bind(R.id.tv_second)
    TextView tvSecond;
    @Bind(R.id.ll_time)
    LinearLayout llTime;
    @Bind(R.id.rl_timing)
    RelativeLayout rlTiming;
    @Bind(R.id.iv_exclusive)
    TextView ivExclusive;
    @Bind(R.id.tv_health_insurance)
    TextView tvHealthInsurance;
    @Bind(R.id.rl_health_insurance)
    RelativeLayout rlHealthInsurance;
    @Bind(R.id.tv_name)
    TextView tvName;
    @Bind(R.id.lv_otc_name)
    LinearLayout lvOtcName;
    @Bind(R.id.tv_subtitle)
    TextView tvSubtitle;
    @Bind(R.id.tv_tax_amount)
    TextView tvTaxAmount;
    @Bind(R.id.tv_original_price)
    TextView tvOriginalPrice;
    @Bind(R.id.tv_control)
    TextView tvControl;
    @Bind(R.id.tv_list)
    MyGridView tvList;
    @Bind(R.id.tv_correction)
    TextView tvCorrection;
    @Bind(R.id.tv_depreciate_inform)
    TextView tvDepreciateInform;
    @Bind(R.id.rl_price_layout)
    RelativeLayout rlPriceLayout;

    @Bind(R.id.rl_health_care_code)
    RelativeLayout rlHealthCareCode;
    @Bind(R.id.tv_health_care_code)
    TextView tvHealthCareCode;

    @Bind(R.id.tv_layout_08)
    TextView tvLayout08;
    @Bind(R.id.tv_validity)
    TextView tvValidity;
    @Bind(R.id.rl_validity)
    RelativeLayout rlValidity;
    @Bind(R.id.rl_validity_layout)
    RelativeLayout rlValidityLayout;
    @Bind(R.id.rl_dateOfManufacture)
    RelativeLayout rlDateOfManufacture;
    @Bind(R.id.tv_dateOfManufacture_content)
    TextView tvDateOfManufactureContent;

    @Bind(R.id.tv_repertory)
    TextView tvRepertory;
    @Bind(R.id.tv_limit)
    TextView tvLimit;
    @Bind(R.id.tv_limit2)
    TextView tvLimit2;
    @Bind(R.id.rl_limit_price)
    RelativeLayout rlLimitPrice;
    @Bind(R.id.tv_ontrol_market)
    TextView tvOntrolMarket;
    @Bind(R.id.tv_product_price_kxj)
    TextView tvProductPriceKxj;
    @Bind(R.id.tv_product_price_ml)
    TextView tvProductPriceMl;
    @Bind(R.id.shop_price_layout)
    RelativeLayout shopPriceLayout;
    @Bind(R.id.ly_product_price_kxj_ml)
    RelativeLayout lyProductPriceKxjMl;
    @Bind(R.id.tv_third_party)
    TextView tvThirdParty;
    @Bind(R.id.rl2_third_party)
    RelativeLayout rl2ThirdParty;
    @Bind(R.id.tv_manufacturers)
    TextView tvManufacturers;
    @Bind(R.id.rl_third_party)
    RelativeLayout rlThirdParty;
    @Bind(R.id.tv_coupon_title_01)
    TextView tvCouponTitle01;
    @Bind(R.id.tv_coupon_title_02)
    TextView tvCouponTitle02;
    @Bind(R.id.tv_coupon_one)
    TextView tvCouponOne;
    @Bind(R.id.tv_coupon_two)
    TextView tvCouponTwo;
    @Bind(R.id.rl_coupon)
    RelativeLayout rlCoupon;
    @Bind(R.id.tv_coupon_title)
    TextView tvCouponTitle;
    @Bind(R.id.tv_icon_type_01)
    TextView tvIconType01;
    @Bind(R.id.iv_icon_type_01)
    ImageView ivIconType01;
    @Bind(R.id.tv_content_type_01)
    TextView tvContentType01;
    @Bind(R.id.ll_01)
    LinearLayout ll01;
    @Bind(R.id.tv_icon_type_02)
    TextView tvIconType02;
    @Bind(R.id.iv_icon_type_02)
    ImageView ivIconType02;
    @Bind(R.id.tv_content_type_02)
    TextView tvContentType02;
    @Bind(R.id.ll_02)
    LinearLayout ll02;
    @Bind(R.id.tv_icon_type_03)
    TextView tvIconType03;
    @Bind(R.id.iv_icon_type_03)
    ImageView ivIconType03;
    @Bind(R.id.tv_content_type_03)
    TextView tvContentType03;
    @Bind(R.id.ll_03)
    LinearLayout ll03;
    @Bind(R.id.ll_show_promotion)
    LinearLayout llShowPromotion;
    @Bind(R.id.rl_coupon_or_promotion)
    ConstraintLayout rlCouponOrPromotion;
    @Bind(R.id.pl_service)
    MyGridView plService;
    @Bind(R.id.iv_service)
    ImageView ivService;
    @Bind(R.id.tv_layout_01)
    TextView tvLayout01;
    @Bind(R.id.tv_spec)
    TextView tvSpec;
    @Bind(R.id.rl_spec)
    RelativeLayout rlSpec;
    @Bind(R.id.tv_subtags)
    ScrollView tvSubtags;
    @Bind(R.id.fyCnAttr)
    FrameLayout fyCnAttr;
    @Bind(R.id.rlvCnAttr)
    RecyclerView rlvCnAttr;
    @Bind(R.id.ivCnAttrArrow)
    ImageView ivCnAttrArrow;
    @Bind(R.id.lyCnmSkuExt)
    LinearLayout lyCnMedineSkuExt;
    ProductDetailCnMedineAttrAdapter commodityKeyAttrAdapter;
    @Bind(R.id.tv_layout_02)
    TextView tvLayout02;
    @Bind(R.id.tv_medium_package)
    TextView tvMediumPackage;
    @Bind(R.id.tv_possible_to_disassemble)
    TextView tvPossibleToDisassemble;
    @Bind(R.id.rl_medium_package)
    RelativeLayout rlMediumPackage;
    @Bind(R.id.tv_layout_03)
    TextView tvLayout03;
    @Bind(R.id.tv_letter_package)
    TextView tvLetterPackage;
    @Bind(R.id.rl_letter_package)
    RelativeLayout rlLetterPackage;
    @Bind(R.id.tv_control_layout_03)
    TextView tvControlLayout03;
    @Bind(R.id.tv_control_price)
    TextView tvControlPrice;
    @Bind(R.id.rl_control)
    RelativeLayout rlControl;
    @Bind(R.id.tv_layout_04)
    TextView tvLayout04;
    @Bind(R.id.tv_suggested_retail_price)
    TextView tvSuggestedRetailPrice;
    @Bind(R.id.tv_suggested_retail_price_02)
    TextView tvSuggestedRetailPrice02;
    @Bind(R.id.rl_suggested_retail_price_02)
    RelativeLayout rlSuggestedRetailPrice02;
    @Bind(R.id.rl_suggested_retail_price)
    RelativeLayout rlSuggestedRetailPrice;
    @Bind(R.id.tv_layout_05)
    TextView tvLayout05;
    @Bind(R.id.tv_manufacturer)
    TextView tvManufacturer;
    @Bind(R.id.rl_manufacturer)
    RelativeLayout rlManufacturer;
    @Bind(R.id.tv_layout_06)
    TextView tvLayout06;
    @Bind(R.id.tv_approval_number)
    TextView tvApprovalNumber;
    @Bind(R.id.tv_approval_number2)
    TextView tvApprovalNumber2;
    @Bind(R.id.rl_medical_insurance_code)
    RelativeLayout tvMedicalInsuranceCode;
    @Bind(R.id.medical_insurance_code_content)
    TextView medicalInsuranceCodeContent;
    @Bind(R.id.rl_producer)
    RelativeLayout rlProducer;
    @Bind(R.id.tv_producer)
    TextView tvProducer;
    @Bind(R.id.tv_medical_insurance_copy)
    TextView tvMiCopy;

    @Bind(R.id.tv_producer_content)
    TextView tvProducerContent;
    @Bind(R.id.rl_approval_number)
    RelativeLayout rlApprovalNumber;
    @Bind(R.id.tv_layout_08_gone)
    TextView tvLayout08Gone;
    @Bind(R.id.tv_validity_gone)
    TextView tvValidityGone;
    @Bind(R.id.rl_validity_gone)
    RelativeLayout rlValidityGone;
    @Bind(R.id.tv_layout_09)
    TextView tvLayout09;
    @Bind(R.id.tv_grossMargin)
    TextView tvGrossMargin;
    @Bind(R.id.rl_grossMargin)
    RelativeLayout rlGrossMargin;
    @Bind(R.id.tv_recommend)
    TextView tvRecommend;
    @Bind(R.id.recommend_layout)
    CommodityRecommendLayout recommendLayout;
    @Bind(R.id.rl_recommend)
    LinearLayout rlRecommend;
    @Bind(R.id.iv_image)
    ImageView ivImage;
    @Bind(R.id.tv_company_name)
    TextView tvCompanyName;
    @Bind(R.id.tv_on_shop)
    TextView tvOnShop;
    @Bind(R.id.tv_pop_put_away)
    TextView tvPutPopAway;
    @Bind(R.id.tv_self_put_away)
    TextView tvSelfPutAway;
    @Bind(R.id.ll_company_name)
    LinearLayout llCompanyName;
    @Bind(R.id.one)
    ConstraintLayout one;
    @Bind(R.id.two)
    LinearLayout two;
    @Bind(R.id.snwtv_commodity_pop_shop)
    ShopNameWithTagView snwtvCommodityPopShop;
    @Bind(R.id.snwtv_commodity_self_shop)
    ShopNameWithTagView snwtvCommoditySelfShop;
    @Bind(R.id.tv_price_prefix)
    TextView tvPricePrefix;

    @Bind(R.id.ll_specification_tv)
    TextView llSpecificationTv;
    @Bind(R.id.ll_specification)
    LinearLayout llSpecification;
    @Bind(R.id.il_specification)
    ImageLayout ilSpecification;
    @Bind(R.id.il_about)
    ImageView ilAbout;
    @Bind(R.id.three)
    LinearLayout three;
    @Bind(R.id.detail_ll_cart)
    LinearLayout detailLlCart;
    @Bind(R.id.ll_detail_tv)
    TextView llDetailTv;
    @Bind(R.id.ll_detail_rl)
    RelativeLayout llDetailRl;
    @Bind(R.id.ll_detail_fl)
    FrameLayout llDetailFl;
//    @Bind(R.id.ll_on_line_service)
//    LinearLayout llOnLineService;
    @Bind(R.id.el_edit)
    ProductEditLayout4 elEdit;
    @Bind(R.id.add_remind)
    RoundTextView addRemind;
    @Bind(R.id.ll_remind)
    LinearLayout llRemind;
    @Bind(R.id.add_cart)
    RoundTextView addCart;
    @Bind(R.id.tv_seckill_pre)
    RoundTextView tvSeckillPre;
    @Bind(R.id.specification_list)
    CommodityRecyclerLayout mSpecificationList;
    @Bind(R.id.inventory_cb)
    CheckBox inventoryCb;
//    @Bind(R.id.detail_ll_inventory)
//    LinearLayout detailLlInventory;
    @Bind(R.id.tv_layout_11)
    TextView tvLayout11;
    @Bind(R.id.tv_inventory)
    TextView tvInventory;
    @Bind(R.id.rl_layout_02)
    RelativeLayout rlLayout02;
    @Bind(R.id.tv_layout_11_02)
    TextView tvLayout1102;
    @Bind(R.id.tv_suggested_retail_price_03)
    TextView tvSuggestedRetailPrice03;
    @Bind(R.id.rl_suggested_retail_price_03)
    RelativeLayout rlSuggestedRetailPrice03;
    @Bind(R.id.tv_health_insurance_price)
    TextView tvHealthInsurancePrice;
    @Bind(R.id.tv_layout_warm_prompt)
    TextView tvLayoutWarmPrompt;
    @Bind(R.id.ll_label)
    LinearLayout llLabel;
    @Bind(R.id.rl_product_bjp_tip)
    RelativeLayout rlProductBjpTip;
    @Bind(R.id.tv_audit_passed_visible)
    TextView tvAuditPassedVisible;
    @Bind(R.id.tv_audit_no_passed)
    TextView tvAuditNoPassed;
    @Bind(R.id.cl_audit_no_passed)
    ConstraintLayout clAuditNoPassed;
    @Bind(R.id.layoutCombined)
    FrameLayout layoutCombined;
    CombinedBuySingleLayout combinedBuySingleLayout;
    CombinedBuyMultiLayout combinedBuyMultiLayout;
    @Bind(R.id.rl_next_day)
    ConstraintLayout rlNextDay;
    @Bind(R.id.tv_next_day_label)
    TextView tvNextLabel;
    @Bind(R.id.tv_next_day)
    TextView tvNextDes;
    @Bind(R.id.rl_detail_parent)
    RelativeLayout rlDetailParent;
    @Bind(R.id.rl_self_shop)
    LinearLayout rlSelfShop;
    @Bind(R.id.iv_self_shop_logo)
    ImageView ivSelfShopLogo;
    @Bind(R.id.tv_self_shop_name)
    TextView tvSelfShopName;
    @Bind(R.id.detail_ll_self_shop)
    LinearLayout llSelfShop;
    @Bind(R.id.marquee_view)
    MarqueeViewSpellGroup marqueeView;
    //限时补价拼团品
    @Bind(R.id.ll_spell_group_limited_time_premium)
    LinearLayout llSpellGroupLimitedTimePremium;
    @Bind(R.id.tv_time_hundred_ms)
    TextView tvMs;
    @Bind(R.id.tv_time_s)
    TextView tvS;
    @Bind(R.id.tv_time_min)
    TextView tvMin;
    @Bind(R.id.tv_time_h)
    TextView tvH;
    @Bind(R.id.tv_spell_limit_time_price)
    TextView tvSpellLimitTimePrice;
    @Bind(R.id.tv_spell_group_limit_content)
    TextView tvSpellGroupLimitContent;
    //拼单开始
    @Bind(R.id.ll_spell_group_root)
    LinearLayout llSpellGroupRoot;
    //拼单未开始
    @Bind(R.id.ll_no_start_spell_group_root)
    LinearLayout llNoStartSpellGroupRoot;
    //拼单开始-拼单价
    @Bind(R.id.tv_spell_group_price)
    TextView tvSpellGroupPrice;
    //拼单未开始-拼单价
    @Bind(R.id.tv_no_start_spell_group_price)
    TextView tvNoStartSpellGroupPrice;
    //拼单未开始-开抢时间
    @Bind(R.id.tv_no_start_spell_group_time)
    TextView tvNoStartSpellGroupTime;
    //拼单开始-原价
    @Bind(R.id.tv_spell_group_original_price)
    TextView tvSpellGroupOriginalPrice;
    //限时加补折后价
    @Bind(R.id.tv_spell_limit_time_origin_price)
    TextView tvSpellLimitTimeOriginPrice;
    //拼单开始-原价
//    @Bind(R.id.progress)
//    ProgressBar progress;
    //拼单开始-已拼数量
    @Bind(R.id.tv_spell_group_already)
    TextView tvSpellGroupAlready;
    //拼单开始-起拼数量
//    @Bind(R.id.tv_spell_group_initial)
//    TextView tvSpellGroupInitial;
    //拼单-轮播
    @Bind(R.id.ll_spell_group_sub_title)
    LinearLayout llSpellGroupSubTitle;
    //拼单-轮播文案
    @Bind(R.id.tv_spell_group_sub_title)
    TextView tvSpellGroupSubTitle;
    //去拼团-按钮
    @Bind(R.id.add_spell_group)
    RoundTextView addSpellGroup;
    @Bind(R.id.lySpellGroupTimeContent)
    LinearLayout lySpellGroupTimeContent;
    //去拼团-倒计时
    @Bind(R.id.tv_spell_group_hour)
    TextView tvSpellGroupHour;
    @Bind(R.id.tv_spell_group_minute)
    TextView tvSpellGroupMinute;
    @Bind(R.id.tv_spell_group_second)
    TextView tvSpellGroupSecond;
    //近效期、临期商品不退换提示语
    @Bind(R.id.tv_tip_optimize)
    TextView tvTipOptimize;
    @Bind(R.id.tv_limited)
    TextView tvLimited;
    @Bind(R.id.nsv_product_detail)
    NestedScrollView nsvProductDetail;
    @Bind(R.id.ll_instructions)
    LinearLayout llInstructions;
    @Bind(R.id.ll_module_recommend)//这个布局不能gone 只能invisible
    LinearLayout llModuleRecommend;
    @Bind(R.id.tv_recommend_tips)//猜你喜欢提示文案
    TextView tvRecommendTips;
    @Bind(R.id.crv_recommend)
    RecyclerView crvRecommend;
    //运费
    @Bind(R.id.rl_freight_tips)
    RelativeLayout rlFreightTips;
    @Bind(R.id.tv_freight_tips)
    TextView tvFreightTips;
    //追溯码
    @Bind(R.id.rl_traceable_tips)
    RelativeLayout rlTraceableTips;
    @Bind(R.id.tv_traceable_tips)
    TextView tvTraceableTips;
    //麻黄碱
    @Bind(R.id.tv_ephedrine)
    TextView tvEphedrine;
    //套餐
    @Bind(R.id.ll_combo)
    LinearLayout llCombo;
    @Bind(R.id.stl_combo)
    SlidingTabLayout stlCombo;
    @Bind(R.id.tv_combo_more)
    TextView tvComboMore;
    @Bind(R.id.vps_combo)
    ViewPagerSlide vpsCombo;
    @Bind(R.id.ll_recommend_word)
    LinearLayout llRecommendWord;
    @Bind(R.id.tv_recommend_word_zan)
    TextView tvRecomendWordZan;
    @Bind(R.id.tv_recommend_word_star)
    TextView tvRecomendWordStar;
    @Bind(R.id.tv_recommend_word_check)
    TextView tvRecomendWordCheck;
    @Bind(R.id.tv_customer_service)
    TextView tvCustomerService;//联系客服或商家
    @Bind(R.id.ll_service_tag)
    LinearLayout llServiceTag;
    //    @Bind(R.id.tv_spell_group_percent)
//    TextView tvSpellGroupPercent;
    @Bind(R.id.ll_share)
    LinearLayout llShare;
    @Bind(R.id.tv_spell_group_aptitude)
    TextView tvSpellGroupAptitude;
    @Bind(R.id.group_spell_group_price)
    Group groupSpellGroupPrice;
    @Bind(R.id.tv_spell_group_time)
    TextView tvCountDownDes;
    @Bind(R.id.ll_bottom_btn)
    LinearLayout llBottomBtn;
    @Bind(R.id.tv_spell_group_control)
    TextView tvSpellGroupControl;
    @Bind(R.id.v_about_divider)
    View vAboutDivider;
    @Bind(R.id.ll_about)
    LinearLayout llAbout;
    @Bind(R.id.spell_group_view)
    HomeSteadySpellGroupView spellGroupView;
    @Bind(R.id.cl_spell_group_02)
    ConstraintLayout clSpellGroup2;
    @Bind(R.id.cl_spell_group_01)
    ConstraintLayout clSpellGroup1;
    @Bind(R.id.tv_spell_group_price_gross)
    TextView tvSpellGroupPriceGross;
    @Bind(R.id.tv_spell_group_gross_des)
    TextView tvSpellGroupGrossDes;

    // 拼团布局的统一单价控件
    @Bind(R.id.tv_unit_price_bottom)
    TextView tvUnitPriceBottom;
    @Bind(R.id.view_divider1)
    View viewDivider1;
    @Bind(R.id.tv_unit_price_simple)
    TextView tvUnitPriceSimple;

    // 特价/秒杀布局的单价控件（暂时注释，可能不存在）
    // @Bind(R.id.tv_promotion_unit_price)
    // TextView tvPromotionUnitPrice;
    // @Bind(R.id.view_promotion_divider)
    // View viewPromotionDivider;
    // @Bind(R.id.ll_second_line_promotion)
    // LinearLayout llSecondLinePromotion;
    // 拼团未开始布局的单价控件（暂时注释，可能不存在）
    // @Bind(R.id.tv_no_start_unit_price)
    // TextView tvNoStartUnitPrice;
    // @Bind(R.id.ll_no_start_unit_price_line)
    // LinearLayout llNoStartUnitPriceLine;
    // 限时加补拼团布局的单价控件（暂时注释，可能不存在）
    // @Bind(R.id.tv_limited_time_unit_price)
    // TextView tvLimitedTimeUnitPrice;
    // @Bind(R.id.view_limited_time_divider)
    // View viewLimitedTimeDivider;
    @Bind(R.id.view_product_detail_control)
    ProductDetailControlView viewProductDetailControl;
    @Bind(R.id.tv_spell_group_price_gross_origin)
    TextView tvSpellGroupPriceGrossOrigin;
    @Bind(R.id.detail_operation_tool_recommend_goods)
    DetailOperationToolRecommendGoodsView detailOperationToolRecommendGoodsView;
    @Bind(R.id.detail_operation_tool_root)
    LinearLayout detailOperationToolRoot;
    @Bind(R.id.ivZengPinTip)
    ImageView ivZengPinTip;
    @Bind(R.id.llFunction)
    LinearLayout llFunction;
    @Bind(R.id.unit)
    TextView priceUnit;
    @Bind(R.id.iv_promotion_more)
    RoundTextView ivPromotionMore;
    @Bind(R.id.view_promotion_more)
    View viewPromotionMore;
    ConstraintLayout fullBg;
    FrameLayout fullContainer;
    @Bind(R.id.cl_new_tag)
    ConstraintLayout clNewTag;
    @Bind(R.id.tv_new_tag_1)
    TextView tvNewTag1;
    @Bind(R.id.tv_new_tag_2)
    TextView tvNewTag2;
    @Bind(R.id.tv_new_tag_3)
    TextView tvNewTag3;
    @Bind(R.id.ll_new_tag)
    LinearLayout llNewTag;
    // 药品配送信息
    @Bind(R.id.ll_delivery_instructions)
    LinearLayout llDeliveryInstructions;
    // 配送方式
    @Bind(R.id.rl_delivery_method_layout)
    RelativeLayout rlDeliveryMethodLayout;
    @Bind(R.id.tv_delivery_method)
    TextView tvDeliveryMethod;
    // 起配包邮
    @Bind(R.id.rl_parcel_layout)
    RelativeLayout rlParcelLayout;
    @Bind(R.id.tv_parcel)
    TextView tvParcel;
    // 发货省市
    @Bind(R.id.rl_shipping_province_city_layout)
    RelativeLayout rlShippingProvinceCityLayout;
    @Bind(R.id.tv_shipping_province_city)
    TextView tvShippingProvinceCity;
    // 发货时间
    @Bind(R.id.rl_delivery_time_layout)
    RelativeLayout rlDeliveryTimeLayout;
    @Bind(R.id.tv_delivery_time)
    TextView tvDeliveryTime;
    @Bind(R.id.same_specification_list_view)
    SameSpecificationsListView sameSpecificationListView;

    public static final int REMIND_COLLECT_CODE = 10;
    private static int DEPRECIATEINFORMCODE = 2;//跳转请求码
    public int number = 0;    //记录对话框中的数量
    private long mEndLocation = 300;
    private boolean autoRoll;
    private String end_time;
    private long endTime;
    private int[] remainTime;
    private String fob;
    private double fobDouble;
    private long skuId;
    private String shopHomeUrl;//店铺路由地址
    HandlerGoodsDao goodsDao = null;
    public GlideDrawable deawable;

    private ProductDetailBean productDetail;
    private ProductDetailBeanWrapper mDetailBean = null;
    // 组合购、加价购
    private GroupPurchaseInfo mGroupPurchaseInfo;
    private GroupPurchaseInfo mAddPurchaseInfo;
    private boolean showGroupPurchaseFlag = false;
    private boolean showAdditionalPurchaseFlag = false;

    private PriceRangeAdapter rangeAdapter;
    private DetailServiceAdapter mDetailServiceAdapter;

    private ProductDetailPopWindow2 mCopWindow;
    private ShowPromotionPopWindowNew mPopWindowPromotion;
    private ShowSpellGroupPopWindow mPopWindowSpellGroup;
    private ProductDiscountPopWindow mDiscountPopWindow;

    private CountDownTimer countDownTimer;
    private CountDownTimer limitTimePremiumCountDownTimer;
    private MarqueeViewAdapter marqueeViewAdapter = null;
    private ActPtBean actPtBean;
    private SeckillBean actSk;
    private int instructionsTop;
    private int recommendTop;
    private List<RowsBean> recommendList = new ArrayList<>();
    private GoodListAdapterNew recommendAdapter = new GoodsListAdapterNewCategory(recommendList, false);
    private int loadMoreStatus = LOAD_MORE_STATUS_DEFAULT;
    public static final int LOAD_MORE_STATUS_DEFAULT = 0;
    public static final int LOAD_MORE_STATUS_LOADING = 1;
    public static final int LOAD_MORE_STATUS_NO_MORE = 2;
    private View footerView;
    //拼团是否开始
    public boolean isSpellGroupStarted = false;
    public ProductDetailBeanWrapper.ShopInfo shopInfo;

    //套餐
    private List<Fragment> list_fragment;
    private ArrayList<String> list_title;

    private boolean mIsAssemble;
    private boolean mIsWholeSale;

    private String pageId = "";
    private String module = "";
    private String offset = "";
    private String sourceType = "";

    //  极光埋点字段用 start

    private JgTrackBean jgTrackBean = null;
    private ReportPDExtendOuterBean jgPdExtendOuterBean = null;

    //  极光埋点字段用 end

    private Long mPageViewStart = 0L; //页面开始浏览时间戳 用于关闭时埋点计算时长

    @Override
    protected void initData(String content) {
        mCommodityViewModel = new ViewModelProvider(requireActivity()).get(CommodityViewModel.class);
        Bundle argument = getArguments();
        String skuId = null;
        if (argument !=null){
            pageId = argument.getString(PAGE_ID, "0");
            module = argument.getString(MODULE, "");
            offset = argument.getString(OFFSET, "0");
            sourceType = argument.getString(SOURCETYPE, "");
            jgTrackBean = (JgTrackBean)argument.getSerializable(JG_TRACK_BEAN);
            jgPdExtendOuterBean = (ReportPDExtendOuterBean) argument.getSerializable(IntentCanst.JG_JSON_REPORT_PD_EXTEND_OUTER_BEAN);
            skuId = argument.getString("skuId", "");
            showGroupPurchaseFlag = argument.getBoolean(SHOW_GROUPPURCHASE_FLAG,false);
            showAdditionalPurchaseFlag = argument.getBoolean(SHOW_ADDITIONALPURCHASE_FLAG,false);
        }
        // 获取拼团品推荐信息
//        getGroupProductRecommend(skuId + "");
        goodsDao = HandlerGoodsDao.getInstance();
        getCouponShopNumber();
        initView();
        if (!isKaUser) {//ka用户隐藏推荐tab
            getRecommendData();
        }
        initReceiver();
        setCombinedBuyData();
        detailOperationToolRecommendGoodsView.setOnJumpClickCallback(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                //随心拼和立即参团按钮点击
                detailOperationToolRecommendGoodsView.setShopCode(productDetail.shopCode);
                detailOperationToolRecommendGoodsView.setOrgId(productDetail.orgId);
                detailOperationToolRecommendGoodsView.setMainGoodsSkuId(productDetail.getProductId());
                detailOperationToolRecommendGoodsView.setThirdCompany(productDetail.isThirdCompany);
                detailOperationToolRecommendGoodsView.setMainGoodsPId(productDetail.pId);
                initShowSpellGroup(productDetail, actPtBean, detailOperationToolRecommendGoodsView.getSPELL_GROUP_RECOMMEND_TYPE());
                return null;
            }
        });
    }

    /**
     * 获取商品相关促销标签
     * 这里的逻辑是用外层数据ROWS里的taglist判断是否有活动，如果有就展示，没有就隐藏
     * 后端会过滤拼团之类不需要显示的活动来保障逻辑正确
     */
    private void getPromotionData(boolean isAssemble, boolean isWholeSale) {
        if (RangePriceBeanKt.isStep(productDetail.rangePriceBean)) return;
        isSpellGroupStarted = (isAssemble && actPtBean.assembleStatus == 1) || isWholeSale;
//        if(productDetail.tagList != null && productDetail.tagList.size() > 0) { //拼团赠品也要显示有效信息
//            rlCouponOrPromotion.setVisibility(View.VISIBLE);
//        } else {
//            //没有信息就不拉取接口然后隐藏模块
//            rlCouponOrPromotion.setVisibility(View.GONE);
//            return;
////            rlCouponOrPromotion.setVisibility(View.VISIBLE);
////        }else if(isSpellGroupStarted){
////            rlCouponOrPromotion.setVisibility(View.GONE);
//        }
//        llShare.setVisibility(isSpellGroupStarted ? View.VISIBLE : View.GONE);
        RequestParams requestParams = new RequestParams();
        requestParams.put("csuId", "" + skuId);

        HttpManager.getInstance().post(AppNetConfig.SKU_DETAIL_PROMOTION, requestParams, new BaseResponse<SkuDetailPromotionInfo>() {
            @Override
            public void onSuccess(String content, BaseBean<SkuDetailPromotionInfo> obj, SkuDetailPromotionInfo skuDetailPromotionInfo) {
                if (skuDetailPromotionInfo != null && (
                        (skuDetailPromotionInfo.getTagList() != null && skuDetailPromotionInfo.getTagList().size() > 0) ||
                                (skuDetailPromotionInfo.getCouponTagList() != null) && skuDetailPromotionInfo.getCouponTagList().size() > 0)
                ) {
                    if (rlCouponOrPromotion != null) {
                        rlCouponOrPromotion.setVisibility(View.VISIBLE);
                    }
                    //优惠券信息
//                    List<TagBean> temp = new ArrayList<>();
//                    temp.addAll(skuDetailPromotionInfo.getCouponTagList());
//                    temp.addAll(skuDetailPromotionInfo.getCouponTagList());
//                    Log.d("nonononon", temp.size()+"");
//                    setCouponList(temp);
                    setCouponList(skuDetailPromotionInfo.getCouponTagList());
                    //促销信息
                    setShowPromotion(skuDetailPromotionInfo.getTagList());
                    // 商品拼团可加购
                    setShowSpellGroup(skuDetailPromotionInfo, false);
                } else {
                    rlCouponOrPromotion.setVisibility(View.GONE);
                    // 商品拼团可加购
                    setShowSpellGroup(skuDetailPromotionInfo, true);
                }
            }
        });

    }

    @NonNull
    @Override
    public String getCouponEntryType() {
        return CouponEntryType.COUPON_ENTRY_TYPE_COMMODITY;
    }

    public interface OnTopOrBottomListener {
        void onTopOrBottom(boolean isTop);
    }

    private OnTopOrBottomListener mOnTopOrBottomListener = null;

    public void setOnTopOrBottomListener(OnTopOrBottomListener listener) {
        this.mOnTopOrBottomListener = listener;
    }

    public interface OnCheckListener {
        void onCheck(ProductDetailBean productDetail);

        void onIsFavoriteStatus(boolean isFavoriteStatus);
    }

    private OnCheckListener mOnCheckListener = null;

    public void setOnCheckListener(OnCheckListener listener) {
        this.mOnCheckListener = listener;
    }

    public interface OnScrollListener {
        void OnScroll(float scrollY);
    }

    private OnScrollListener mOnScrollListener = null;

    public void setOnScrollListener(OnScrollListener listener) {
        this.mOnScrollListener = listener;
    }

    private RefreshGoodsDetailListener mRefreshListener;

    public interface RefreshGoodsDetailListener {
        void refreshGoodsDetail();
    }

    public void setRefreshListener(RefreshGoodsDetailListener listener) {
        mRefreshListener = listener;
    }

    public void smoothScrollToIndex(int moduleIndex) {
        switch (moduleIndex) {
            case 0:
                nsvProductDetail.smoothScrollTo(0, 0);
                break;
            case 1:
                nsvProductDetail.smoothScrollTo(0, instructionsTop);
                break;
            case 2:
                nsvProductDetail.smoothScrollTo(0, recommendTop);
                break;
        }
    }

    /*
     * 初始化View
     * */
    private void initView() {
        try {
            fullBg = requireActivity().findViewById(R.id.cl_full_bg);
            fullContainer = requireActivity().findViewById(R.id.fl_full_container);
            brandIv.fullBg = this.fullBg;
            brandIv.fullContainer = this.fullContainer;
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (SpUtil.isKa()) {
//            detailLlInventory.setVisibility(View.GONE);
            tvRecommendTips.setVisibility(View.GONE);
        }
        if (tvOriginalPrice == null) {
            return;
        }
        //tvSpellGroupOriginalPrice.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG | Paint.ANTI_ALIAS_FLAG);
        //购物车动画
        llDetailFl.postDelayed(() -> {
            if (llDetailRl != null) {
                llDetailRl.getLocationInWindow(YBMAppLike.endLocationInCommodity);
            }
        }, mEndLocation);

        elEdit.setRtvAddCartClickListener((content, number1) -> {
            btnClickReport(content,number1);
        });
        elEdit.setOnAddCartListener(new ProductEditLayout4.AddCartListener() {
            @Override
            public RequestParams onPreAddCart(RequestParams params) {
                FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, mFlowData, AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_ADDCART_DETAIL);
                if (getActivity() != null) {
                    String nsid = getActivity().getIntent().getStringExtra("nsid");
                    String sdata = getActivity().getIntent().getStringExtra("sdata");
                    if (nsid != null) {
                        params.put("nsid", nsid);
                        params.put("sdata", sdata);
                    }
                    JgRequestParams jgRequestParams = new JgRequestParams();

                    if(JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo() != null){
                        JgOperationPositionInfo mJgOperationInfo = JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo();
                        if (mJgOperationInfo.getProductId()!= null && !mJgOperationInfo.getProductId().isEmpty() && Objects.equals(mJgOperationInfo.getProductId(), productDetail.getProductId())){
                            if (mJgOperationInfo.getOperationId()!=null){
//                    params.put("operationId", mJgOperationInfo.getOperationId());
                                jgRequestParams.setOperation_id(mJgOperationInfo.getOperationId());
                            }
                            if (mJgOperationInfo.getOperationRank() != null){
//                    params.put("operationRank", mJgOperationInfo.getOperationRank().toString());
                                jgRequestParams.setOperation_rank(mJgOperationInfo.getOperationRank());
                            }

                            if (mJgOperationInfo.getRank() != null){
//                    params.put("rank", mJgOperationInfo.getRank().toString());
                                jgRequestParams.setRank(mJgOperationInfo.getRank());
                            }
                        }
                    }
                    if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean() != null){
                        RowsBean mJgSearchRowsBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean();
                        if (mJgSearchRowsBean.getProductId()!=null && !mJgSearchRowsBean.getProductId().isEmpty() && Objects.equals(mJgSearchRowsBean.getProductId(), String.valueOf(skuId))){
                            jgRequestParams.setList_position_type(mJgSearchRowsBean.positionType+"");
                            if (mJgSearchRowsBean.positionTypeName != null){
                                jgRequestParams.setList_position_typename(mJgSearchRowsBean.positionTypeName);
                            }
                            if (mJgSearchRowsBean.searchKeyword != null){
                                jgRequestParams.setKey_word(mJgSearchRowsBean.searchKeyword);
                            }
                            jgRequestParams.setProduct_id(mJgSearchRowsBean.getProductId());
                            jgRequestParams.setProduct_name(mJgSearchRowsBean.getProductName());
                            jgRequestParams.setProduct_first(mJgSearchRowsBean.categoryFirstId);
                            jgRequestParams.setProduct_number(mJgSearchRowsBean.getProductNumber());
                            jgRequestParams.setProduct_price(mJgSearchRowsBean.getJgProductPrice());
                            jgRequestParams.setProduct_type(String.valueOf(mJgSearchRowsBean.productType));
                            jgRequestParams.setProduct_activity_type(mJgSearchRowsBean.productActivityType);
                            jgRequestParams.setProduct_shop_code(mJgSearchRowsBean.shopCode);
                            jgRequestParams.setProduct_shop_name(mJgSearchRowsBean.shopName);

                            if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField() != null){
                                jgRequestParams.setRank(JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getRank());
                                JGPageListCommonBean mJgPageListCommonBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getMJgPageListCommonBean();
                                if (mJgPageListCommonBean != null){
                                    jgRequestParams.setSptype(mJgPageListCommonBean.getSptype());
                                    jgRequestParams.setJgspid(mJgPageListCommonBean.getJgspid());
                                    jgRequestParams.setSid(mJgPageListCommonBean.getSid());
                                    jgRequestParams.setPage_no(mJgPageListCommonBean.getPage_no());
                                    jgRequestParams.setResult_cnt(mJgPageListCommonBean.getResult_cnt());
                                    jgRequestParams.setPage_size(mJgPageListCommonBean.getPage_size());
                                    jgRequestParams.setTotal_page(mJgPageListCommonBean.getTotal_page());
                                }

                            }
                        }
                    }
                    jgRequestParams.setDirect("2");
                    jgRequestParams.setSession_id(com.ydmmarket.report.manager.TrackManager.getSessionId(YBMAppLike.getAppContext()));
                    params.put("mddata",new Gson().toJson(jgRequestParams));
                    if (jgTrackBean != null && jgTrackBean.getEntrance() != null && jgTrackBean.getEntrance().contains(JGTrackManager.TrackShoppingCart.TITLE)){ //购物车只传个direct = "3"
                        params.getParamsMap().remove("mddata");
                    }

                }
                return params;
            }

            @Override
            public void onAddCartSuccess(String number, RequestParams editShopNumberParams) {
                if (productDetail != null) {

                    AddToCart addToCart = new AddToCart();
                    JGPageListCommonBean jgPageListCommonBean = new JGPageListCommonBean();
                    if (jgPdExtendOuterBean != null){
                        jgPageListCommonBean.setSptype(jgPdExtendOuterBean.getSptype());
                        jgPageListCommonBean.setJgspid(jgPdExtendOuterBean.getJgspid());
                        jgPageListCommonBean.setSid(jgPdExtendOuterBean.getSid());
                        jgPageListCommonBean.setResult_cnt(jgPdExtendOuterBean.getResultCnt());
                        jgPageListCommonBean.setPage_no(jgPdExtendOuterBean.getPageNo());
                        jgPageListCommonBean.setPage_size(jgPdExtendOuterBean.getPageSize());
                        jgPageListCommonBean.setTotal_page(jgPdExtendOuterBean.getTotalPage());
                        jgPageListCommonBean.setKey_word(jgPdExtendOuterBean.getKeyWord());
                        addToCart.setSearch_sort_strategy_id(jgPdExtendOuterBean.getSearchSortStrategyId());
                        addToCart.setRank(jgPdExtendOuterBean.getRank());
                        addToCart.setOperation_id(jgPdExtendOuterBean.getOperationId());
                        addToCart.setOperation_rank(jgPdExtendOuterBean.getOperationRank());
                        addToCart.setList_position_type(jgPdExtendOuterBean.getListPositionType());
                        addToCart.setList_position_typename(jgPdExtendOuterBean.getListPositionTypename());
                    }
                    addToCart.setUrl(AppUtilKt.getFullClassName(this));
                    addToCart.setReferrer(AppUtilKt.getFullClassName(this));
                    addToCart.setTitle(JGTrackManager.TrackProductDetail.TITLE);
                    addToCart.setJGPageListCommonBean(jgPageListCommonBean);
                    addToCart.setProduct_id((long) productDetail.id);
                    addToCart.setProduct_name(productDetail.showName);
                    addToCart.setProduct_first(productDetail.categoryFirstId);
                    addToCart.setProduct_price(productDetail.fob);
                    addToCart.setProduct_type(String.valueOf(productDetail.productType));
                    addToCart.setDirect("2");
                    try {
                        addToCart.setProduct_number(Integer.valueOf(number));
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                    addToCart.setProduct_activity_type(productDetail.productActivityType);
                    addToCart.setProduct_shop_code(productDetail.shopCode);
                    try {
                        addToCart.setProduct_shop_name(mDetailBean.shopInfo.shopName);
                    }catch (Exception e){
                        e.printStackTrace();
                    }


                    JGTrackTopLevelKt.reportAddToCart(addToCart);
                }

                newAddCartTrack(productDetail, number);
            }
        });
        try {
            if (getActivity() != null && getActivity().getIntent() != null) {
                String sId = getActivity().getIntent().getStringExtra("sId");
                String spId = getActivity().getIntent().getStringExtra("spId");
                String spType = getActivity().getIntent().getStringExtra("spType");
                elEdit.mSId = sId;
                elEdit.mSpId = spId;
                elEdit.mSpType = spType;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        nsvProductDetail.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (nestedScrollView, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            Context context = getContext();
            if (context != null) {
                ProductDetailScrollListener listener = ((ProductDetailScrollListener) context);
                if (scrollY >= instructionsTop && scrollY < recommendTop) {
                    listener.onProductDetailScroll(1);
                } else if (scrollY < instructionsTop) {
                    listener.onProductDetailScroll(0);
                } else {
                    if (!isKaUser) {

                        listener.onProductDetailScroll(2);
                    }
                }
            }
            if (!isKaUser && scrollY > (nestedScrollView.getChildAt(0).getMeasuredHeight() - nestedScrollView.getMeasuredHeight() - ConvertUtils.dp2px(50)) && loadMoreStatus != LOAD_MORE_STATUS_NO_MORE && loadMoreStatus != LOAD_MORE_STATUS_LOADING) {
                // 底部
                loadMoreStatus = LOAD_MORE_STATUS_LOADING;
                getRecommendData();
            }
        });
        calcModuleTop(200);
        crvRecommend.setLayoutManager(new WrapLinearLayoutManager(getContext(), WrapLinearLayoutManager.VERTICAL, false));
        crvRecommend.setNestedScrollingEnabled(false);
        crvRecommend.setAdapter(recommendAdapter);
        recommendAdapter.setEnableLoadMore(true);
        String mEntrance = "";
        if (jgTrackBean.getEntrance() != null && !jgTrackBean.getEntrance().isEmpty()) {
            if (!jgTrackBean.getEntrance().contains("热销精选")) {
                mEntrance = jgTrackBean.getEntrance() + "(热销精选)";
            } else {
                mEntrance = jgTrackBean.getEntrance();
            }
        } else {
            mEntrance = JGTrackManager.TrackProductDetail.TITLE + "(热销精选)";
        }
        JgTrackBean mJgTrackBean = new JgTrackBean(
                AppUtilKt.getFullClassName(CommodityFragment.this),
                JGTrackManager.TrackProductDetail.TITLE,
                JGTrackManager.TrackProductDetail.TITLE + "-热销精选",
                JGTrackManager.TrackProductDetail.TITLE + "-热销精选",
                JGTrackManager.TrackProductDetail.PAGE_ID,
                JGTrackManager.TrackProductDetail.TITLE,
                mEntrance,
                ""
        );
        mJgTrackBean.setUrl(AppUtilKt.getFullClassName(this));
        mJgTrackBean.setJgReferrer(AppUtilKt.getFullClassName(this));
        recommendAdapter.setJgTrackBean(mJgTrackBean);
//        recommendAdapter.setOnListItemClickListener(rows -> {
//            openUrl("ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + rows.getId(), rows.rowsFlow);
//        });
        if (jgTrackBean != null) {
            brandIv.setJgReferrer(jgTrackBean.getJgReferrer());
        }

        mCommodityViewModel.getSameSpecificationsListLiveData().observe(requireActivity(),response -> {
            if (response !=null && response.getData() != null && response.getData().getRows() != null && !response.getData().getRows().isEmpty()){
                //有数据会显示
                //给拼团秒杀添加时间
                AdapterUtils.INSTANCE.addLocalTimeForRows(response.getData().getRows());
                sameSpecificationListView.setReportPDExtendOuterBean(jgPdExtendOuterBean);
                sameSpecificationListView.setData(response.getData().getRows());
                CommodityDetailReport.setSameSpecificationsScmId(getContext(), response.data.getScmId());
            }else {
                //设置为空会隐藏
                sameSpecificationListView.setReportPDExtendOuterBean(null);
                sameSpecificationListView.setData(new ArrayList<>());
            }

        });
    }

    private void btnClickReport(String content,int number) {
        ReportPDExposure bean = new ReportPDExposure();
        bean.setUrl(AppUtilKt.getFullClassName(this));
        bean.setReferrer(AppUtilKt.getFullClassName(this));
        bean.setTitle(JGTrackManager.TrackProductDetail.TITLE);
        bean.setAccountId(SpUtil.getAccountId());
        bean.setMerchantId(SpUtil.getMerchantid());
        bean.setOuterBean(jgPdExtendOuterBean);
        bean.setProductId(productDetail.id);
        bean.setProductName(productDetail.showName);
        bean.setProductFirst(productDetail.categoryFirstId);
        bean.setProductPrice(mDetailBean.getJgProductPrice());
        bean.setProductType(String.valueOf(productDetail.productType));
        bean.setProductShopCode(productDetail.shopCode);
        bean.setProductActivityType(productDetail.productActivityType);
        if (shopInfo != null) {
            bean.setProductShopName(shopInfo.shopName);
        }
        ReportPDButtonClick reportButtonClick = new ReportPDButtonClick(bean);
        reportButtonClick.setBtnName(content);
        reportButtonClick.setProductNumber(number);
        reportButtonClick.setBtnDesc("商详页");
        reportButtonClick.setDirect("2");
        ReportManager.getInstance().report(reportButtonClick);
    }

    private void newAddCartTrack(
            ProductDetailBean productDetail,
            String number) {
        //新 加购埋点
        HashMap<String, Object> map = new HashMap<>();
        map.put(TrackManager.FIELD_SKU_ID, productDetail.id);
        map.put(TrackManager.FIELD_SKU_NAME, productDetail.showName);
        map.put(TrackManager.FIELD_SKU_NUM, number);
        map.put(TrackManager.FIELD_SKU_PRICE, productDetail.fob);
        map.put(TrackManager.FIELD_BUTTON, sourceType);
        map.put(TrackManager.FIELD_PAGE_ID, pageId);
        map.put(TrackManager.FIELD_MODULE, module);
        map.put(TrackManager.FIELD_OFFSET, offset);
        TrackManager.Companion.clickEventTrack(TrackManager.EVENT_ACTION_ADDLIST_CLICK, map);
    }

    /**
     * 计算view位置
     *
     * @param delayMillis
     */
    private void calcModuleTop(long delayMillis) {
        llInstructions.postDelayed(() -> {
            //防止llInstructions、llModuleRecommend 还未初始化页面就已经关闭
            if (llInstructions != null && llModuleRecommend != null) {
                instructionsTop = llInstructions.getTop();
                recommendTop = llModuleRecommend.getTop();
            }
        }, delayMillis);
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_commodity;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mPageViewStart = System.currentTimeMillis();
    }

    @Override
    public void onDetach() {
        super.onDetach();
        if (productDetail != null) {
            productViewCloseJGTrack(mDetailBean, productDetail, shopInfo);
        }
    }

    private void productViewCloseJGTrack(ProductDetailBeanWrapper mDetailBean,
                                         ProductDetailBean productDetail,
                                         ProductDetailBeanWrapper.ShopInfo shopInfo) {
        try {
            String productType = "";
            String jgReferrer = "";
            String jgReferrerTitle = "";
            String jgReferrerModule = "";

            if (jgTrackBean != null) {
                if (jgTrackBean.getJgReferrer() != null) {
                    jgReferrer = jgTrackBean.getJgReferrer();
                }
                if (jgTrackBean.getJgReferrerTitle() != null) {
                    jgReferrerTitle = jgTrackBean.getJgReferrerTitle();
                }
                if (jgTrackBean.getJgReferrerModule() != null) {
                    jgReferrerModule = jgTrackBean.getJgReferrerModule();
                }
            }



            String shopName = "";
            if (shopInfo != null) {
                shopName = shopInfo.shopName;
            }
            Long duration = System.currentTimeMillis() - mPageViewStart;
            HashMap<String, Object> map = new HashMap<>();
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_ID, productDetail.id);
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_NAME, productDetail.showName);
            map.put(JGTrackManager.FIELD.FIELD_SHOP_ID, productDetail.shopCode);
            map.put(JGTrackManager.FIELD.FIELD_SHOP_NAME, shopName);
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_TYPE, mDetailBean.getJgProductType());
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_FIRST, productDetail.categoryFirstId);
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_PRESENT_PRICE, productDetail.fob);
            map.put(JGTrackManager.FIELD.FIELD_REFERRER, jgReferrer);
            map.put(JGTrackManager.FIELD.FIELD_REFERRER_TITLE, jgReferrerTitle);
            map.put(JGTrackManager.FIELD.FIELD_REFERRER_MODULE, jgReferrerModule);
            map.put(JGTrackManager.FIELD.FIELD_DURATION, duration);

            JGTrackManager.Companion.eventTrack(requireActivity(), JGTrackManager.TrackProductDetail.EVENT_PRODUCT_VIEW_CLOSE, map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取商品详情
     *
     * @param productDetail 商品详情-实体类
     */
    public void upDataUI(ProductDetailBeanWrapper mDetailBean, ProductDetailBean productDetail,
                         ProductDetailBeanWrapper.ShopInfo shopInfo, boolean isAssemble, boolean isWholeSale, ActPtBean actPtBean, SeckillBean actSk, List<CommodityComboBean> commodityComboBeanList) {
        if (rlTiming == null) {
            return;
        }
        trackCommodityPv(productDetail.id+"");
        if (isWholeSale) {
            productDetail.actPtBean = actPtBean;
        }
        if (productDetail.limitFullDiscountActInfo != null){ //限时加补也需要赋值
            productDetail.actPtBean = actPtBean;
        }
        brandIv.productId = productDetail.id + "";
        brandIv.productName = productDetail.showName;
        String sourceType = "";
        try {
            sourceType = getActivity().getIntent().getStringExtra("sourceType");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (getActivity() != null) {
            String searchSortStrategyId = getActivity().getIntent().getStringExtra("search_sort_strategy_id");
            String index = getActivity().getIntent().getStringExtra("index");
            FlowDataEventAnalysisKt.flowDataPageCommodityDetailsForCode(mFlowData, productDetail.id + "", AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_INTOPRODUCTDETAIL_REAL, sourceType, index, searchSortStrategyId);
        }
        String nsid = getActivity().getIntent().getStringExtra("nsid");
        String sdata = getActivity().getIntent().getStringExtra("sdata");
        if (nsid != null) {
            if (sdata == null) sdata = "";
            FlowDataEventAnalysisKt.flowDataPageCommodityDetailsForFeed(nsid, sdata, productDetail.getProductId(), productDetail.showName, productDetail.barcode, AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_INTOPRODUCTDETAIL_REAL);
        }
        this.mDetailBean = mDetailBean;
        this.productDetail = productDetail;
        this.shopInfo = shopInfo;
        this.actPtBean = actPtBean;
        this.actSk = actSk;
        if (null != productDetail) {

            jgTrackBean.setUrl(AppUtilKt.getFullClassName(this));
            jgTrackBean.setJgReferrer(AppUtilKt.getFullClassName(this));
            jgTrackBean.setProductId(productDetail.getProductId());
            jgTrackBean.setProductType(mDetailBean.getJgProductType());

            showProgress();
            //详情
            setDetailData(productDetail, isAssemble, isWholeSale, actPtBean, actSk, commodityComboBeanList);
            //图文说明书
            setSpecificationData(productDetail);
            //文字说明书
            getSkuFindSkuInstruction(productDetail);
            //配送说明
            setDeliveryInstructions(mDetailBean);

            if (productDetail.isVirtualSupplier == false){ //不是虚拟店铺才请求
                mCommodityViewModel.getSameSpecificationsList(productDetail.id+"");
            }
            // 设置店铺信息，由于后端忙不过来，所以这里区分了自营控销和pop两种场景
            // pop商家保持原逻辑，通过接口取数据
            // 自营和控销通过商品详情的shopInfo字段获取数据
            if (productDetail.isThirdCompany == 1) {
                //pop商家基本信息
                shopHomeUrl = shopInfo.shopUrl;
//                llSelfShop.setVisibility(View.VISIBLE);
                getCompanyName(productDetail);
            } else {
                //自营店铺和自然人控销店铺基本信息
                setShopInfo(shopInfo);
            }


            if (SpUtil.isKa()) {//ka用户隐藏 纠错 降价通知 为你推荐
                tvCorrection.setVisibility(View.GONE);
                tvDepreciateInform.setVisibility(View.GONE);
                rlRecommend.setVisibility(View.GONE);
                rlTiming.postDelayed(this::dismissProgress, mEndLocation);
            } else {
                //极力推荐
                rlTiming.postDelayed(() -> getRecommendedResult(productDetail.categoryId), mEndLocation);

            }
        }
        calcModuleTop(0);
        if (productDetail.isThirdCompany == 1) {
            vAboutDivider.setVisibility(View.GONE);
            llAbout.setVisibility(View.GONE);
        }

        if (productDetail != null) {
            productViewJGTrack(productDetail, shopInfo);
            productDetailJGTrack(productDetail, shopInfo, false);
        }

        setStepPriceDiscount(productDetail.tagList);
    }

    /**
     * 设置商详阶梯价优惠信息
     * @param tagList
     */
    private void setStepPriceDiscount(List<LabelIconBean> tagList) {
        if (tagList == null) {
            rlCouponOrPromotion.setVisibility(View.GONE);
            return;
        }
        if (!RangePriceBeanKt.isStep(productDetail.rangePriceBean)) return;
        //阶梯价隐藏查看更多按钮
        ivPromotionMore.setVisibility(View.GONE);
        List<TagBean> tagBeanList = new ArrayList<>();
        for (LabelIconBean tagBean : tagList) {
            TagBean tb = new TagBean();
            tb.uiStyle = tagBean.uiStyle;
            tb.text = tagBean.text;
            tb.textColor = tagBean.textColor;
            tb.bgColor = tagBean.bgColor;
            tb.borderColor = tagBean.borderColor;
            tb.description = tagBean.description;
            tagBeanList.add(tb);
        }
        setShowPromotion(tagBeanList);
    }

    private void productViewJGTrack(ProductDetailBean productDetail, ProductDetailBeanWrapper.ShopInfo shopInfo) {

        try {
            String productType = "";
            String jgReferrer = "";
            String jgReferrerTitle = "";
            String jgReferrerModule = "";
            String activityEntrance = "";
            String jgEntrance = "";

            if (jgTrackBean != null) {
                jgReferrer = jgTrackBean.getJgReferrer();
                jgReferrerTitle = jgTrackBean.getJgReferrerTitle();
                jgReferrerModule = jgTrackBean.getJgReferrerModule();
                activityEntrance = jgTrackBean.getActivityEntrance();
                jgEntrance = jgTrackBean.getEntrance();
            }

            String shopName = "";
            if (shopInfo != null) {
                shopName = shopInfo.shopName;
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_ID, productDetail.id);
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_NAME, productDetail.showName);
            map.put(JGTrackManager.FIELD.FIELD_SHOP_ID, productDetail.shopCode);
            map.put(JGTrackManager.FIELD.FIELD_SHOP_NAME, shopName);
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_TYPE, mDetailBean.getJgProductType());
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_FIRST, productDetail.categoryFirstId);
            map.put(JGTrackManager.FIELD.FIELD_PRODUCT_PRESENT_PRICE, productDetail.fob);
            map.put(JGTrackManager.FIELD.FIELD_ACTIVITY_ENTRANCE, activityEntrance);
            map.put(JGTrackManager.FIELD.FIELD_ENTRANCE, jgEntrance);
            map.put(JGTrackManager.FIELD.FIELD_REFERRER, jgReferrer);
            map.put(JGTrackManager.FIELD.FIELD_REFERRER_TITLE, jgReferrerTitle);
            map.put(JGTrackManager.FIELD.FIELD_REFERRER_MODULE, jgReferrerModule);

            JGTrackManager.Companion.eventTrack(requireActivity(), JGTrackManager.TrackProductDetail.EVENT_PRODUCT_VIEW, map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void productDetailJGTrack(ProductDetailBean productDetail, ProductDetailBeanWrapper.ShopInfo shopInfo, Boolean isFromSpell) {
        try {
            ReportPDExposure bean = new ReportPDExposure();
            bean.setUrl(AppUtilKt.getFullClassName(this));
            bean.setReferrer(AppUtilKt.getFullClassName(this));
            bean.setTitle(JGTrackManager.TrackProductDetail.TITLE);
            bean.setAccountId(SpUtil.getAccountId());
            bean.setMerchantId(SpUtil.getMerchantid());
            bean.setOuterBean(jgPdExtendOuterBean);
            bean.setProductId(productDetail.id);
            bean.setProductName(productDetail.showName);
            bean.setProductFirst(productDetail.categoryFirstId);
            bean.setProductPrice(mDetailBean.getJgProductPrice());
            bean.setProductType(String.valueOf(productDetail.productType));
            bean.setProductShopCode(productDetail.shopCode);
            bean.setProductActivityType(productDetail.productActivityType);
            if (shopInfo != null) {
                bean.setProductShopName(shopInfo.shopName);
            }
            if (isFromSpell) {
                ReportPDButtonClick reportButtonClick = new ReportPDButtonClick(bean);
                reportButtonClick.setBtnName("立即参团");
                reportButtonClick.setBtnDesc("商详页底部弹窗");
                reportButtonClick.setDirect("0");
                ReportManager.getInstance().report(reportButtonClick);
            } else {
                ReportManager.getInstance().report(bean);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
     * 店铺
     * */
    private void setShopInfo(ProductDetailBeanWrapper.ShopInfo shopInfo) {
        if (shopInfo != null) {
            rlSelfShop.setVisibility(View.VISIBLE);
//            llSelfShop.setVisibility(View.VISIBLE);
//            detailLlInventory.setVisibility(View.GONE);
            if (shopInfo.shopName != null) {
                shopHomeUrl = shopInfo.shopUrl;
                tvSelfShopName.setText(shopInfo.shopName);
                String url = shopInfo.shopLogoUrl;
                if (!url.startsWith("http")) {
                    url = AppNetConfig.LORD_TAG + url;
                }
                ImageHelper.with(getNotNullActivity())
                        .load(url).placeholder(R.drawable.icon_goods_detail_shop_logo_default)
                        .error(R.drawable.icon_goods_detail_shop_logo_default)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate()
                        .transform(new CenterCrop(getNotNullActivity()), new GlideRoundTransform(getNotNullActivity(), 4))
                        .into(ivSelfShopLogo);
                List<TagBean> tagList = new ArrayList<>();
                TagBean tagBean = new TagBean();
                tagBean.bgColor = "#F2FBF8";
                tagBean.borderColor = "#7DD8BA";
                tagBean.text = "自营";
                tagBean.textColor = "#00AE6E";
                tagBean.uiStyle = 1;
                tagList.add(tagBean);
//                if (shopInfo.shopPropTags == null) shopInfo.shopPropTags = new ArrayList<>();
//                shopInfo.shopPropTags.add(tagBean);
//                TextViewKt.TextWithPrefixTag(tvSelfShopName, tagList, shopInfo.shopName, 2);
                boolean hasSymbol = true;
                if (TextUtils.isEmpty(shopInfo.shelvesTag)) {
                    shopInfo.shelvesTag = "";
                    hasSymbol = false;
                }
                if (TextUtils.isEmpty(shopInfo.salesVolumeTag)) {
                    shopInfo.salesVolumeTag = "";
                    hasSymbol = false;
                }
                String selfPutAwayStr = "";
                if (hasSymbol) {
                    selfPutAwayStr = shopInfo.shelvesTag + " | " + shopInfo.salesVolumeTag;
                } else {
                    selfPutAwayStr = shopInfo.shelvesTag + shopInfo.salesVolumeTag;
                }
                tvSelfPutAway.setText(selfPutAwayStr);
                snwtvCommoditySelfShop.bindData(shopInfo.shopPropTags, "");
            }
        }
    }

    /**
     * 设置数据
     *
     * @param productDetail 商品详情-实体类
     */
    private void setSpecificationData(ProductDetailBean productDetail) {
        if (llSpecification == null) {
            return;
        }
        List<ProductDetailImageBean> images = productDetail.getSkuInstructionImageList();
        if (images != null && images.size() > 0) {
            ilSpecification.bindData(images);
            ilSpecification.setVisibility(View.VISIBLE);
            llSpecificationTv.setVisibility(View.VISIBLE);
            llSpecification.setVisibility(View.VISIBLE);
        } else {
            ilSpecification.setVisibility(View.GONE);
            llSpecificationTv.setVisibility(View.GONE);
            llSpecification.setVisibility(View.GONE);
        }
    }

    /**
     * 设置商品配送说明
     * @param mDetailBean
     */
    private void setDeliveryInstructions(ProductDetailBeanWrapper mDetailBean) {
        if (mDetailBean.deliveryInfo == null) {
            llDeliveryInstructions.setVisibility(View.GONE);
            return;
        } else {
            llDeliveryInstructions.setVisibility(View.VISIBLE);
            // 配送方式
            if (mDetailBean.deliveryInfo.deliveryMode == null) {
                rlDeliveryMethodLayout.setVisibility(View.GONE);
            } else {
                rlDeliveryMethodLayout.setVisibility(View.VISIBLE);
                tvDeliveryMethod.setText(mDetailBean.deliveryInfo.deliveryMode);
            }
            // 起购包邮
            if (mDetailBean.deliveryInfo.freeShipping == null) {
                rlParcelLayout.setVisibility(View.GONE);
            } else {
                rlParcelLayout.setVisibility(View.VISIBLE);
                tvParcel.setText(mDetailBean.deliveryInfo.freeShipping);
            }
            // 发货省市
            if (mDetailBean.deliveryInfo.shippingProvinceCity == null) {
                rlShippingProvinceCityLayout.setVisibility(View.GONE);
            } else {
                rlShippingProvinceCityLayout.setVisibility(View.VISIBLE);
                tvShippingProvinceCity.setText(mDetailBean.deliveryInfo.shippingProvinceCity);
            }
            // 发货时间
            if (mDetailBean.deliveryInfo.deliveryTime == null) {
                rlDeliveryTimeLayout.setVisibility(View.GONE);
            } else {
                rlDeliveryTimeLayout.setVisibility(View.VISIBLE);
                tvDeliveryTime.setText(mDetailBean.deliveryInfo.deliveryTime);
            }
        }
    }

    /**
     * 获取商品说明书
     */
    public void getSkuFindSkuInstruction(ProductDetailBean productDetail) {
        long id = productDetail.id;
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        if (id > 0) {
            params.put("id", id + "");
        }
        HttpManager.getInstance().post(AppNetConfig.SKU_FINDSKUINSTRUCTION, params, new BaseResponse<List<ProductInstructionBean>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<ProductInstructionBean>> obj, List<ProductInstructionBean> beans) {
                if (two == null) {
                    return;
                }
                if (obj != null && obj.isSuccess()) {
                    if (beans != null && beans.size() > 0) {
                        setData(beans);
                    }
                }
                calcModuleTop(0);
            }

            @Override
            public void onFailure(NetError error) {

            }
        });
    }

    /**
     * 说明书
     *
     * @param data 商品详情-实体类
     */
    public void setData(List<ProductInstructionBean> data) {
        if (two == null) {
            return;
        }

        if (data == null || data.isEmpty()) {
            two.setVisibility(View.GONE);
            return;
        } else {
            two.setVisibility(View.VISIBLE);
        }

        mSpecificationList.setItemData(data);

    }

    /*
     * 获取厂家基本信息
     * */
    private void getCompanyName(ProductDetailBean productDetail) {

        if (productDetail.isThirdCompany == 1) {//pop商家
            llCompanyName.setVisibility(View.VISIBLE);
            tvOnShop.setVisibility(View.VISIBLE);
            //不是三方的商家就不调用这个接口
            String orgId = productDetail.orgId;
            String merchantId = SpUtil.getMerchantid();
            RequestParams params = new RequestParams();
            params.put("merchantId", merchantId);
            if (!TextUtils.isEmpty(orgId)) {
                params.put("orgId", orgId);
            }
            HttpManager.getInstance().post(AppNetConfig.GET_POPCOMPANY_DETAIL, params, new BaseResponse<PopMerchantsBean>() {

                @Override
                public void onSuccess(String content, BaseBean<PopMerchantsBean> obj, PopMerchantsBean beans) {
                    if (one == null) {
                        return;
                    }
                    if (obj != null && obj.isSuccess()) {
                        if (beans != null) {
                            setCompanyData(beans);
                        }
                    }
                }

                @Override
                public void onFailure(NetError error) {

                }
            });
        }
    }

    /*
     * 设置厂家基本信息
     * */
    private void setCompanyData(PopMerchantsBean bean) {

        String putaway = "上架" + bean.upSkuNum + "种";
        String staleLine = bean.salesVolumeDesc;
        String staleNum = bean.saleSkuNum + "";
        String salesVolume = bean.salesVolume;
        if (!TextUtils.isEmpty(staleLine) && !TextUtils.isEmpty(salesVolume)) {
            String sale = staleLine.replace("xxx", salesVolume);
            SpannableStringBuilder activityStr = new SpannableStringBuilder(sale);
//            int startIndex = staleLine.indexOf("xxx");
//            int endIndex = startIndex + staleNum.length();
//            activityStr.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.text_00B377)), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            putaway = putaway + " | " + sale;
        }

        List<TagBean> tagList = new ArrayList<>();
        if (bean.qualityTag != null) {
            tagList.add(bean.qualityTag);
        }
//        TextViewKt.TextWithPrefixTag(tvCompanyName, tagList, bean.orgName, 1);
        tvCompanyName.setText(bean.orgName);
        tvPutPopAway.setText(putaway);
        snwtvCommodityPopShop.bindData(bean.shopPropTags, "");

        if (TextUtils.isEmpty(bean.orgLogo)) {
            ImageHelper.with(getNotNullActivity()).load(R.drawable.icon_goods_detail_shop_logo_default).into(ivImage);
        } else {
            String markerUrl = bean.orgLogo;
            if (!markerUrl.startsWith("http")) {
                markerUrl = AppNetConfig.LORD_TAG + markerUrl;
            }
            ImageHelper.with(getNotNullActivity())
                    .load(markerUrl)
                    .placeholder(R.drawable.icon_goods_detail_shop_logo_default)
                    .error(R.drawable.icon_goods_detail_shop_logo_default)
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .dontAnimate()
                    .transform(new CenterCrop(getNotNullActivity()), new GlideRoundTransform(getNotNullActivity(), 4))
                    .into(ivImage);
        }
    }

    @SuppressLint("RestrictedApi")
    public void setAutoRoll(boolean autoRoll) {
        this.autoRoll = autoRoll;
        if (tvTime != null && tvTime.getVisibility() == View.GONE || endTime <= 0) {
            this.autoRoll = false;
        }
        if (autoRoll) {
            remainTime = null;
            ArchTaskExecutor.getInstance().executeOnDiskIO(() -> {

                if (actSk != null && actSk.status == 1 && (actSk.getSurplusTime() - System.currentTimeMillis() + actSk.responseLocalTime) > 0) {
                    remainTime = DateTimeUtil.getRemainTime(actSk.currentTime + System.currentTimeMillis() - actSk.responseLocalTime, actSk.endTime);
                    ArchTaskExecutor.getInstance().postToMainThread(() -> {
                        setTimeRunnable();
//                        llTime.setVisibility(View.GONE);
                    });

                } else {
                    ArchTaskExecutor.getInstance().postToMainThread(() -> {
                        tvTime.setText("已结束");
                        llTime.setVisibility(View.GONE);
                    });
                }
            });
        }
    }

    private void setTimeRunnable() {
        if (rlTiming == null) {
            return;
        }
        if (limitHandler != null) {
            limitHandler.removeCallbacks(runnable);
        }
        if (tvTime.getVisibility() == View.GONE || endTime <= 0) {
            return;
        }
        llTime.setVisibility(View.VISIBLE);
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 文字 “签署协议后可见” 替换为商品价格；
        if (productDetail.controlType == 5) {
            // 如果是特价商品要显示价格
            if (!TextUtils.isEmpty(productDetail.pricePrefix)) {
                // 显示特价价格
                rlTiming.setVisibility(View.VISIBLE);
                rlSuggestedRetailPrice03.setVisibility(View.VISIBLE);
            }
        } else if (productDetail.isOEM && productDetail.signStatus == 0) {
            rlTiming.setVisibility(View.GONE);//倒计时模块
        } else {
            if (TextUtils.isEmpty(productDetail.controlTitle)) {
                rlTiming.setVisibility(View.VISIBLE);
            } else {
                rlTiming.setVisibility(View.GONE);
            }
        }
        limitHandler.post(runnable);
    }

    /*
     * 倒计时
     * */
    private static Handler limitHandler = new Handler();
    private Runnable runnable = new Runnable() {

        @Override
        public void run() {
            if (limitHandler == null) {
                return;
            }
            if (tvTime == null) {
                limitHandler.removeCallbacks(runnable);
            }

            if (tvTime.getVisibility() == View.GONE || endTime <= 0) {
                limitHandler.removeCallbacks(runnable);
                return;
            }
            if (!autoRoll) {
                limitHandler.removeCallbacks(runnable);
                return;
            }
            if (remainTime == null) {
                setAutoRoll(true);
                return;
            }
            if (remainTime == null) {
                setAutoRoll(true);
                return;
            } else {
                remainTime[3] -= 1;
                if (remainTime[3] == -1) {
                    remainTime[2] -= 1;
                    remainTime[3] = 59;
                    if (remainTime[2] == -1) {
                        remainTime[1] -= 1;
                        remainTime[2] = 59;
                        if (remainTime[1] == -1) {
                            remainTime[0] -= 1;
                            remainTime[1] = 23;
                            if (remainTime[0] == -1) {//结束了
                                remainTime = new int[]{0, 0, 0, 0};
                            }
                        }
                    }
                }
            }
            if (remainTime[0] <= 0 && remainTime[1] <= 0 && remainTime[2] <= 0 && remainTime[3] <= 0) { // 验证结束时间
                tvTime.setText("已结束");
                llTime.setVisibility(View.GONE);
                return;
            }

            if (remainTime[0] <= 0) {
                tvDay.setVisibility(View.GONE);
                tvDotDay.setVisibility(View.GONE);
            } else {
                tvDay.setVisibility(View.VISIBLE);
                tvDotDay.setVisibility(View.VISIBLE);
            }
            tvDay.setText(remainTime[0] + "天");
            tvDotDay.setText(":");
            tvHour.setText((remainTime[1] > 9 ? "" : "0") + remainTime[1]);
            tvDotHour.setText(":");
            tvMinute.setText((remainTime[2] > 9 ? "" : "0") + remainTime[2]);
            tvSecond.setText((remainTime[3] > 9 ? "" : "0") + remainTime[3]);
            limitHandler.postDelayed(runnable, 1000);

        }
    };

    /**
     * 极力推荐
     * url => "product/findRecommendedProduct"
     */
    public void getRecommendedResult(int categoryId) {
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        if (categoryId > 0) {
            params.put("categoryId", categoryId + "");
        }
        HttpManager.getInstance().post(AppNetConfig.FINDRECOMMENDEDPRODUCT, params, new BaseResponse<RowsListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RowsListBean> obj, RowsListBean bean) {

                if (recommendLayout == null) {
                    return;
                }
                if (bean != null) {
                    BaseFlowData recommendFlowData = new BaseFlowData(bean.sptype, bean.spid, bean.sid, null, "");
                    recommendLayout.setFlowData(recommendFlowData);
                    recommendLayout.setJgTrackBean(jgTrackBean);
                    FlowDataEventAnalysisKt.flowDataPageCommoditySearch(recommendFlowData);
                }
                dismissProgress();
                if (obj != null) {
                    if (obj.isSuccess()) {

                        if (bean != null && bean.rows != null && bean.rows.size() > 0) {
                            recommendLayout.setItemData(bean.rows);
                            rlRecommend.setVisibility(View.VISIBLE);
                        } else {
                            rlRecommend.setVisibility(View.GONE);
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }

    /*
     * 设置otc图片数据
     * */
    private void bindImageData(final ProductDetailBean productDetail) {
/*
        // 文字左边的图片排序为从从左到右
        // 1
        ArrayList imageUrls = new ArrayList<>();
        if (productDetail.activityTag != null && !TextUtils.isEmpty(productDetail.activityTag.tagUrl)) {
            String url = productDetail.activityTag.tagUrl;
            if (!productDetail.activityTag.tagUrl.startsWith("http")) {
                url = AppNetConfig.LORD_TAG + productDetail.activityTag.tagUrl;
            }
            imageUrls.add(url);
        }
        // 2
        if (productDetail.gift) {
            // list.add(getNotNullActivity().getResources().getDrawable(R.drawable.icon_procurement_festival));
            imageUrls.add(R.drawable.icon_procurement_festival);
        }
        // 3
        if (productDetail.isThirdCompany == 0) {
            // list.add(getNotNullActivity().getResources().getDrawable(R.drawable.icon_autotrophy_new));
            imageUrls.add(R.drawable.icon_autotrophy_new);
        }
        // 4
        if (!TextUtils.isEmpty(productDetail.drugClassificationImage)) {
            String url = productDetail.drugClassificationImage;
            if (!productDetail.drugClassificationImage.startsWith("http")) {
                url = AppNetConfig.LORD_TAG + productDetail.drugClassificationImage;
            }
            imageUrls.add(url);
        }
        UiUtilsKt.getProductNameWithPicTag(getNotNullActivity(), tvName, productDetail.showName, imageUrls);*/

        try {
            List<Drawable> list = new ArrayList<>();

            if (!TextUtils.isEmpty(productDetail.drugClassificationImage)) {
                String url = productDetail.drugClassificationImage;
                if (!productDetail.drugClassificationImage.startsWith("http")) {
                    url = AppNetConfig.LORD_TAG + productDetail.drugClassificationImage;
                }

                ImageHelper.with(getNotNullActivity()).load(url)
                        .placeholder(R.drawable.jiazaitu_min)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate().dontTransform().into(new SimpleTarget<GlideDrawable>() {
                            @Override
                            public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                                list.add(resource);
                                setShowOtc(productDetail, list);
                            }

                            @Override
                            public void onLoadFailed(Exception e, Drawable errorDrawable) {
                                super.onLoadFailed(e, errorDrawable);
                                setShowOtc(productDetail, list);
                            }
                        });
            } else {
                setShowOtc(productDetail, list);
            }

        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    private void setShowOtc(ProductDetailBean productDetail, List<Drawable> list) {
        //自营
        if (productDetail.isThirdCompany == 0) {
            //虚拟供应商
            if (productDetail.isVirtualSupplier) {
                list.add(ContextCompat.getDrawable(getNotNullActivity(), R.drawable.icon_goods_tag_hot));
            } else {
                list.add(getNotNullActivity().getResources().getDrawable(R.drawable.icon_autotrophy_new));
            }
        }

        if (productDetail.gift) {
            list.add(getNotNullActivity().getResources().getDrawable(R.drawable.icon_procurement_festival));
        }

        try {

            if (productDetail.activityTag != null && !TextUtils.isEmpty(productDetail.activityTag.tagUrl)) {
                String url = productDetail.activityTag.tagUrl;
                if (!productDetail.activityTag.tagUrl.startsWith("http")) {
                    url = AppNetConfig.LORD_TAG + productDetail.activityTag.tagUrl;
                }

                ImageHelper.with(getNotNullActivity())
                        .load(url)
                        .placeholder(R.drawable.jiazaitu_min)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate().dontTransform().into(new SimpleTarget<GlideDrawable>() {
                            @Override
                            public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                                list.add(resource);
                                CommodityFragment.this.deawable = resource;
                                setShowActivityTag(productDetail, list);
                            }
                        });

            } else {
                setShowActivityTag(productDetail, list);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    private void setShowActivityTag(ProductDetailBean productDetail, List<Drawable> list) {
        SpannableStringBuilder shopName = getShopNameIcon(tvName, productDetail.showName, list);
        if (!TextUtils.isEmpty(shopName)) tvName.setText(shopName);
    }

    private SpannableStringBuilder getShopNameIcon(TextView tvName, String shopName, List<Drawable> icons) {
        if (shopName != null && icons != null && icons.size() > 0) {
            SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
            for (int i = 0; i < icons.size(); i++) {
                Drawable drawable = icons.get(i);
                //适配图标大小问题
                float tvHeight = tvName.getTextSize();
                float drawableWith = (drawable.getIntrinsicWidth() / (float) drawable.getIntrinsicHeight()) * tvHeight;
                drawable.setBounds(0, 0, (int) (drawableWith + 0.5), (int) (tvHeight + 0.5));

                MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
                //占个位置
                spannableString.insert(0, "-");
                spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
            return spannableString;
        }
        return null;
    }

    /**
     * 设置数据
     *
     * @param productDetail          商品详情-实体类
     * @param commodityComboBeanList
     */
    private void setDetailData(ProductDetailBean productDetail, boolean isAssemble, boolean isWholeSale, ActPtBean actPtBean, SeckillBean actSk, List<CommodityComboBean> commodityComboBeanList) {
        mIsAssemble = isAssemble;
        mIsWholeSale = isWholeSale;
        detailOperationToolRecommendGoodsView.setProductDetail(productDetail);
        detailOperationToolRecommendGoodsView.setPdExtendOuterBean(jgPdExtendOuterBean);
        detailOperationToolRecommendGoodsView.setShopInfo(shopInfo);
        //如果是随心拼商品更新底部操作栏
        if (productDetail.canAddToCart) {
            detailOperationToolRecommendGoodsView.setVisibility(View.VISIBLE);
            rlDetailParent.setVisibility(View.GONE);
//            detailOperationToolRoot.setVisibility(View.GONE);
            if (actPtBean != null && actPtBean.supportSuiXinPin) {
                detailOperationToolRecommendGoodsView.setSuiXinPinBubble(actPtBean.suiXinPinButtonBubbleText);
            }
            detailOperationToolRecommendGoodsView.setActPtType(isAssemble, isWholeSale);
            detailOperationToolRecommendGoodsView.setCanAddToCart(productDetail.canAddToCart);
            detailOperationToolRecommendGoodsView.setCanAddToCart(productDetail);
        } else {
            if (actPtBean != null && actPtBean.supportSuiXinPin) {
                detailOperationToolRecommendGoodsView.setVisibility(View.VISIBLE);
                rlDetailParent.setVisibility(View.GONE);
//                detailOperationToolRoot.setVisibility(View.GONE);
                detailOperationToolRecommendGoodsView.setSuiXinPinBubble(actPtBean.suiXinPinButtonBubbleText);
            } else {
                detailOperationToolRecommendGoodsView.setVisibility(View.GONE);
//                detailOperationToolRoot.setVisibility(View.VISIBLE);
                detailOperationToolRecommendGoodsView.registerRecommendGoodsJumpType(SPELL_GROUP_RECOMMEND_DEFAULT, false);
            }
            detailOperationToolRecommendGoodsView.setActPtType(isAssemble, isWholeSale);
        }
        detailOperationToolRecommendGoodsView.setJgTrackBean(jgTrackBean);

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("commodityName", productDetail.showName);
            jsonObject.put("commodityCode", productDetail.id);
            jsonObject.put("commodityBrand", "");
            jsonObject.put("commodityCategory", productDetail.categoryId);
            jsonObject.put("commodityLevel", "");
            jsonObject.put("activityCategory", "");
            //赠品埋点
            jsonObject.put("productType", productDetail.productType);
            jsonObject.put("isGive", productDetail.isGive);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        XyyIoUtil.track(XyyIoUtil.ACTION_COMMODITYDETAILS, jsonObject);

        if (tvTaxAmount == null) {
            return;
        }

        skuId = productDetail.id;

        // 获取折后价
        if (!productDetail.isShowGive()) {
            getPriceAfterDiscount(skuId + "");
        }

        // 获取营销信息
        if (!RangePriceBeanKt.isStep(productDetail.rangePriceBean)) {
            getPromotionData(isAssemble, isWholeSale);
        } else {
            rlCouponOrPromotion.setVisibility(View.VISIBLE);
        }

        if (mOnCheckListener != null) {
            mOnCheckListener.onCheck(productDetail);
        }
        //设置商品基础信息
        setCommodityBase(productDetail, isAssemble, isWholeSale, actPtBean);

        //商品区间价格
        setProductPriceRanges(productDetail);

        // 价格前缀，如特价、直降价、秒杀价
        processPricePrefix(productDetail, isAssemble, isWholeSale);

        //产品信息
        getDrugInformation(productDetail);
        //V12.0.06 新增：展示中药属性
        setCnMedineAttr(productDetail);

        //设置医保标签等标识
        setTvSubtags(productDetail);

        //是否售罄  显示到货提醒
        setRemind(productDetail);

        //描述-跳转链接
        setDescription(productDetail);

        // 如果是保健品显示“本品不能替代药” 提示
        // 在商品信息中批准文号为国食健字、卫食健字、食健备的保健食品的商品详情页中增加【本品不能代替药物】字样，此处仅是字样，无链接。
        // PC端和APP端的商品详情页都需要增加此字样。
        setApprovalNumber(productDetail);


        //是否拼单
        setIsAssemble(productDetail, isAssemble);
        //设置批购包邮
        setWholeSale(isWholeSale, actPtBean, productDetail);

        if (isAssemble) {
//            int onLineServiceIndex = llBottomBtn.indexOfChild(llOnLineService);
//            llBottomBtn.removeView(llSelfShop);
//            llBottomBtn.addView(llSelfShop, onLineServiceIndex);
        }

        //促销价
        setPreferential(productDetail);

        //如果控销价不存在，全部不显示
        boolean isUniformPrice = StringUtil.isEmpty(productDetail.suggestPrice) && StringUtil.isEmpty(productDetail.uniformPrice);

        //如果建议零售价存在或者是控销并且不可购买，控销不显示
        setUniformPrice(productDetail, isUniformPrice);

        /*==============控销并且不可购买，控销和毛利率不显示===============
         * 商品设置了控销价，同时设置了价格区间，则商品在用户端显示控销价；
         * 若商品在促销活动中的设置了促销价格，同时该商品设置了价格区间，则商品在用户端显示促销价；
         * **/

        /*
         *  ==============控销并且不可购买，控销和毛利率不显示===============是否控销
         *  isControl=1表示控销，=2表示不是控销
         *  需要隐藏的字段有：含税价、原价、控销零售价（包括主图右侧和商品信息列表）、建议零售价
         * */
        boolean isBuy = true;
        isBuy = setControlAndOEM(productDetail, isUniformPrice, isBuy);

        //加减按钮
        elEdit.bindData(productDetail.id, productDetail.status, isBuy, ProductEditLayout.FROMPAGE_COMMODITY, false, productDetail.mediumPackageNum, productDetail.isSplit == 1,jgPdExtendOuterBean);
        //改变加减框的样式
        elEdit.modifyViewStyle(R.color.white, R.drawable.icon_tool_add_detail, R.drawable.icon_tool_minus_detail, R.color.color_292933);
        RowsBean rowsBean = new RowsBean();
        rowsBean.setImageUrl(productDetail.imageUrl);
        rowsBean.setShowName(productDetail.showName);
        rowsBean.setNearEffect(productDetail.nearEffect);
        rowsBean.setAvailableQty(productDetail.availableQty);
        rowsBean.mediumPackageNum = productDetail.mediumPackageNum;
        rowsBean.fob = productDetail.fob;
        rowsBean.setId(productDetail.id);
        rowsBean.setOrgId(productDetail.orgId);
        rowsBean.isSplit = productDetail.isSplit;
        rowsBean.setSpec(productDetail.spec);
        rowsBean.mediumPackageNum = productDetail.mediumPackageNum;
        rowsBean.rangePriceBean = productDetail.rangePriceBean;

        rowsBean.jgTrackBean = jgTrackBean;
        if (productDetail.tags != null) {
            rowsBean.tags = productDetail.tags;
        }
        elEdit.rowsBean = rowsBean;
        elEdit.jgTrackBean = jgTrackBean;
        elEdit.reportPDExtendOuterBean = jgPdExtendOuterBean;
        elEdit.isAddCartShowPopupWindow = true;
        //是否为赠品
        setShowGive(productDetail);
        //视频播放-商品轮播
        setListData(productDetail.getImagesVideosList());
        //设置商品名称前面otc图片数据
        // bindImageData(productDetail);
        //服务条款
        setService(productDetail);
        //限购
        setPromotionTag(productDetail);
        //售罄-下架
        setSellAndSoldStatus(productDetail);
        //未签署协议隐藏极力推荐
        ShowOemType(productDetail);
        //处理一审状态
        handleAuditPassedVisible(productDetail);
        //设置优先购的购买权限
        setAddAndUnableStatus(productDetail.merchantStatus, productDetail.distanceEndTime);

        //修改近远效期ui样式
        setMarginUiStyle();

        //设置套餐数据显示
        setComboData(commodityComboBeanList);

        //设置商品详情卖点推荐语展示
        setRecommendWord(productDetail);

        //设置联系客服还是联系商家
        setContacts(productDetail);

        //秒杀倒计时
        setSeckillEndTime(actSk);
//        setOffShelf();

        // 设置控销
        setControl();

        if (productDetail.isShowGive()) {
            //赠品商详 隐藏建议零售价
            rlSuggestedRetailPrice02.setVisibility(View.GONE);
        }
        //批购包邮隐藏建议零售价
        if (isWholeSale) {
            rlSuggestedRetailPrice02.setVisibility(View.GONE);
        }

        // 判断detailOperationToolRecommendGoodsView 可见 ，另外就不可见
        if (detailOperationToolRecommendGoodsView.getVisibility() == View.VISIBLE){
            setAllControlButtonGone();
            rlDetailParent.setVisibility(View.GONE);
        }
    }

    /**
     * 设置控销
     */
    public void setControl() {
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 拼团价 道具时那一块 文字 “签署协议后可见” 替换为商品价格；
        if (productDetail.controlType == 5) {
            // 是否是拼团商品
            if (productDetail.productType == 3) {
                rlPriceLayout.setVisibility(View.GONE); // 正常的价格显示
            }
            // 如果是特价商品要显示价格
            if (!TextUtils.isEmpty(productDetail.pricePrefix)) {
                // 显示特价价格
                rlTiming.setVisibility(View.VISIBLE);
            }

            viewProductDetailControl.setControlData(productDetail, isSpellGroupStarted);
            setControlButton();
            return;
        }

        if (!TextUtils.isEmpty(productDetail.controlTitle)) {
            llSpellGroupRoot.setVisibility(View.GONE);
            tvAuditPassedVisible.setVisibility(View.GONE);
            llNoStartSpellGroupRoot.setVisibility(View.GONE);
            rlPriceLayout.setVisibility(View.GONE);
            rlTiming.setVisibility(View.GONE);
            tvPricePrefix.setVisibility(View.GONE);
            llSpellGroupLimitedTimePremium.setVisibility(View.GONE);
        }
        viewProductDetailControl.setControlData(productDetail, isSpellGroupStarted);
        setControlButton();
    }

    /**
     * 设置控销按钮
     */
    private void setControlButton() {
        if (!TextUtils.isEmpty(productDetail.controlTitle)) {
            setAllControlButtonGone();
            if (productDetail.controlType == 1) {
                clAuditNoPassed.setVisibility(View.VISIBLE);
            } else {
                addCart.setVisibility(View.VISIBLE);
                addCart.setText(productDetail.controlPurchaseButton);
            }
        }
    }

    private void setAllControlButtonGone() {
        elEdit.setVisibility(View.GONE);
        llRemind.setVisibility(View.GONE);
        addCart.setVisibility(View.GONE);
        addSpellGroup.setVisibility(View.GONE);
        tvSeckillPre.setVisibility(View.GONE);
        clAuditNoPassed.setVisibility(View.GONE);
    }


    //<editor-fold desc=" 获取拼团品推荐 ">
    // 业务场景： 当该商品为非拼团品时，增加拼团品的推荐引流，提升拼团品的转化

    CommodityViewModel mCommodityViewModel = null;

    public void handleGroupProductRecommend(String skuid, CommodityGroupRecommondBean t) {
        if (t == null || t.getRows() == null || t.getRows().isEmpty()) return;
        spellGroupView.setVisibility(View.VISIBLE);
        // 曝光埋点
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sku_id1", skuid);
            jsonObject.put("action", t.getJumpUrl());
            XyyIoUtil.track(XyyIoUtil.PAGE_COMMODITYDETAILS_SHOPWINDOW_EXPOSURE, jsonObject);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        View header = View.inflate(getContext(), R.layout.header_commodity_spell_group, null);
        TextView tvTitle = header.findViewById(R.id.tv_title);
        ImageView ivTitleEndfix = header.findViewById(R.id.iv_title_endfix);
        TextView tvMore = header.findViewById(R.id.tv_more_entry);
        tvTitle.setText(t.getMainTitle());
        ImageUtil.load(getContext(), t.getSubTitleUrl(), ivTitleEndfix);
        tvMore.setText(t.getJumpName());
        tvMore.setOnClickListener(v -> {
            RoutersUtils.open(t.getJumpUrl());
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("sku_id", skuid);
                jsonObject.put("action", t.getJumpUrl());
                XyyIoUtil.track(XyyIoUtil.ACTION_COMMODITY_SPELL_GROUP_TITLE, jsonObject);
                if (jgTrackBean != null) {
                    JGTrackTopLevelKt.jgTrackProductDetailBtnClick(getActivity(), "大家都在团", jgTrackBean.getJgReferrer());
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        });

        spellGroupView.setHeader(header);
        spellGroupView.setFooterHeight(10f);
        spellGroupView.setFooterCorner(false);
        spellGroupView.setOnSpellGroupCallback(new ISpellGroupAnalysisCallback() {
            @Override
            public void onSpellGroupGoodsClick(@Nullable String id, @Nullable String activity_id, @Nullable String offset, @Nullable String actionUrl) {
                try {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("sku_id", skuid);
                    jsonObject.put("recommendSku_id", id);
                    jsonObject.put("activity_id", activity_id);
                    jsonObject.put("offset", offset);
                    XyyIoUtil.track(XyyIoUtil.ACTION_COMMODITY_SPELL_GROUP_ITEM, jsonObject);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                if (actionUrl != null) {
                    CmsPageParam cmsPageParam = t.getCmsPageParam();
                    if (cmsPageParam != null && actionUrl.startsWith("ybmpage://")) {
                        FlowData flowData = new FlowData(cmsPageParam.getSptype(), cmsPageParam.getSpid(), cmsPageParam.getSid(), "", "", null);
                        openUrl(actionUrl, flowData);
                    } else {
                        RoutersUtils.open(actionUrl);
                    }
                }
            }

            @Override
            public void onSpellGroupGoodsExposure(@Nullable String id, @Nullable String activity_id, @Nullable String offset) {

                try {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("sku_id1", skuid);
                    jsonObject.put("sku_id2", id);
                    jsonObject.put("activity_id", activity_id);
                    jsonObject.put("offset", offset);
                    XyyIoUtil.track(XyyIoUtil.PAGE_COMMODITYDETAILS_SHOPWINDOWCOMMODITY_EXPOSURE, jsonObject);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        });
        spellGroupView.setGoodsData(t.getRows(), t.getLicenseStatus() == null ? 0 : t.getLicenseStatus(), "");
    }
    //</editor-fold>


    private void setContacts(ProductDetailBean productDetail) {//pop店铺联系商家
//        tvCustomerService.setText(productDetail.isThirdCompany == 1 ? "联系商家" : "客服");
    }

    private void setRecommendWord(ProductDetailBean productDetail) {//卖点推荐语
        String sellingProposition1 = productDetail.sellingProposition1;
        String sellingProposition2 = productDetail.sellingProposition2;
        String sellingProposition3 = productDetail.sellingProposition3;
        if (TextUtils.isEmpty(sellingProposition1) && TextUtils.isEmpty(sellingProposition2) && TextUtils.isEmpty(sellingProposition3)) {
            llRecommendWord.setVisibility(View.GONE);
            return;
        }
        llRecommendWord.setVisibility(View.VISIBLE);
        tvRecomendWordZan.setVisibility(TextUtils.isEmpty(sellingProposition1) ? View.GONE : View.VISIBLE);
        tvRecomendWordZan.setText(sellingProposition1);
        tvRecomendWordStar.setVisibility(TextUtils.isEmpty(sellingProposition2) ? View.GONE : View.VISIBLE);
        tvRecomendWordStar.setText(sellingProposition2);
        tvRecomendWordCheck.setVisibility(TextUtils.isEmpty(sellingProposition3) ? View.GONE : View.VISIBLE);
        tvRecomendWordCheck.setText(sellingProposition3);
    }

    private void setComboData(List<CommodityComboBean> commodityComboBeanList) {//套餐相关
        if (commodityComboBeanList == null || commodityComboBeanList.size() <= 0) {
            return;
        }
        List<CommodityComboBean> newList = commodityComboBeanList;
        if (commodityComboBeanList.size() > 3) {//只取前三个套餐
            newList = commodityComboBeanList.subList(0, 3);
        }
        list_fragment = new ArrayList<>();
        list_title = new ArrayList<>();
        for (CommodityComboBean comboBean : newList) {
            ComboFragment comboFragment = new ComboFragment();
            Bundle bundle = new Bundle();
            bundle.putString("packageId", comboBean.getPackageId());
            bundle.putString("packagePrice", comboBean.getPackagePrice());
            bundle.putString("skuNum", comboBean.getSkuNum());
            bundle.putString("subtotalPrice", comboBean.getSubtotalPrice());
            bundle.putParcelableArrayList("skuList", (ArrayList<? extends Parcelable>) comboBean.getSkuList());
            comboFragment.setArguments(bundle);
            list_fragment.add(comboFragment);
            list_title.add(comboBean.getPackageTitle());
        }
        llCombo.setVisibility(View.VISIBLE);
        ComboPagerAdapter pagerAdapter = new ComboPagerAdapter(getChildFragmentManager(), list_fragment, list_title);
        vpsCombo.setAdapter(pagerAdapter);
        vpsCombo.setOffscreenPageLimit(list_title.size() + 1);
        vpsCombo.setSlide(false);
        stlCombo.setViewPager(vpsCombo);
        stlCombo.setTitles(list_title);
        stlCombo.setIndicatorWidthEqualTitleHalf(true);
        //跳转链接
        tvComboMore.setVisibility(TextUtils.isEmpty(commodityComboBeanList.get(0).getDescriptionUrl()) ? View.GONE : View.VISIBLE);
        tvComboMore.setOnClickListener(v -> {//套餐app调转路径
            RoutersUtils.open(commodityComboBeanList.get(0).getDescriptionUrl());
        });
    }

    // 获取折后价相关信息
    private void getPriceAfterDiscount(String skuId) {
        RequestParams requestParams = new RequestParams();
        requestParams.put("skuId", skuId);
        requestParams.setUrl(AppNetConfig.DETAIL_MESSAGE_DISCOUNT);

        HttpManager.getInstance().post(requestParams, new BaseResponse<ProductDetailPriceAfterDiscountBean>() {

            @Override
            public void onSuccess(String content, BaseBean<ProductDetailPriceAfterDiscountBean> obj, ProductDetailPriceAfterDiscountBean bean) {
                if (obj != null && bean != null) {
                    double showPriceAfterDiscount;
                    mDiscountData = bean;
                    if (bean.getPrice() == null) return;
                    try {
                        showPriceAfterDiscount = Double.parseDouble(bean.getPrice().replace("¥", ""));
                        if (productDetail.limitFullDiscountActInfo != null && showPriceAfterDiscount < productDetail.limitFullDiscountActInfo.getLimitFullDiscount()){ //限时加补品 不显示折后价
//                            tvSpellLimitTimeOriginPrice.setVisibility(View.VISIBLE);
//                            tvSpellLimitTimeOriginPrice.setText("折后约" + bean.getPrice());
                            return;
                        }

                        if (productDetail.actPtBean != null && productDetail.actPtBean.isStepPrice()){ //拼团品阶梯价
                            if (showPriceAfterDiscount < Double.parseDouble(productDetail.actPtBean.getMinSkuPrice())){
                                showDiscount(bean);
                            }
                        }else if (RangePriceBeanKt.isStep(productDetail.rangePriceBean)){ //普通品阶梯价
                            if (showPriceAfterDiscount < Double.parseDouble(productDetail.rangePriceBean.getMinSkuPrice())){
                                showDiscount(bean);
                            }
                        }else if( showPriceAfterDiscount <= mDetailBean.getShowPrice()){
                            showDiscount(bean);
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        });

    }

    private void showDiscount(ProductDetailPriceAfterDiscountBean bean){
        if (!TextUtils.isEmpty(bean.getPrice())) {
            tvOriginalPrice.setVisibility(View.VISIBLE);
            tvOriginalPrice.setText("折后约" + bean.getPrice());
            tvTaxDiscountPrice.setVisibility(View.VISIBLE);
            tvTaxDiscountPrice.setText("折后约" + bean.getPrice());
            tvSpellGroupOriginalPrice.setVisibility(View.VISIBLE);
            tvSpellGroupOriginalPrice.setText("折后约" + bean.getPrice());
        }
    }

    private void setMarginUiStyle() {
        if (rlProductBjpTip.getVisibility() != View.VISIBLE && rlHealthCareCode.getVisibility() != View.VISIBLE) {
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) rlValidityLayout.getLayoutParams();
            params.topMargin = 0;
            rlValidityLayout.setLayoutParams(params);
        }
    }

    private void setCommodityBase(ProductDetailBean productDetail, boolean isAssemble, boolean isWholeSale, ActPtBean actPtBean) {
        //活动价
        boolean isShowUrl = productDetail.isReducePrice() && productDetail.isMarkerUrl();
        String activityPrice = "药采节价:" + UiUtils.transform(productDetail.reducePrice);
        tvActivityPrice.setText(activityPrice);
        tvActivityPrice.setVisibility(isShowUrl ? View.VISIBLE : View.INVISIBLE);

        // 是否显示医保价
        String healthInsurancePrice = "医保支付价:¥" + UiUtils.transform(productDetail.prescriptionPrice);
        tvHealthInsurancePrice.setText(healthInsurancePrice);
        tvHealthInsurancePrice.setVisibility(productDetail.prescriptionPrice <= 0.00 ? View.GONE : View.VISIBLE);
        //库存
        if ((isAssemble && actPtBean.assembleStatus == 1) || actSk != null) {
            //拼团
            String availableQty = "可购数量:" + (productDetail.availableQty > 100 ? "大于100" : "" + productDetail.availableQty);
            tvRepertory.setText(availableQty);
        } else {
            String availableQty = "库存:" + (productDetail.availableQty > 100 ? "大于100" : "" + productDetail.availableQty);
            tvRepertory.setText(availableQty);
        }
        //麻黄碱
        tvEphedrine.setVisibility(productDetail.isEphedrine == 1 ? View.VISIBLE : View.GONE);
        // 原价
//        if (productDetail.retailPrice == 0) {
//            tvOriginalPrice.setVisibility(View.GONE);
//        } else {
//            String retailPrice = "¥" + UiUtils.transform(productDetail.retailPrice);
//            tvOriginalPrice.setText(retailPrice);
//        }
        //含税价
        fob = "¥" + UiUtils.transform(productDetail.fob);
        fobDouble = productDetail.fob;
        tvTaxAmountTime.setText(productDetail.fob + "元/" + productDetail.productUnit);

        ((TextView) getView().findViewById(R.id.unit)).setText(generateUnit(productDetail.productUnit));
        //清单
        inventoryCb.setChecked(productDetail.isListStatus());
        tvInventory.setText(productDetail.isListStatus() ? "已加入" : "加常购");
        tvInventory.setActivated(productDetail.isListStatus());
        initInventory(productDetail, inventoryCb);
        //是否显示第三方厂家
        tvManufacturers.setText(productDetail.companyName);
        //设置商品名称
        tvName.setText(productDetail.showName);
        //rlCouponOrPromotion.setVisibility(!productDetail.isShowVoucher && (productDetail.getCxTagList() == null || productDetail.getCxTagList().isEmpty()) ? View.GONE : View.VISIBLE);
        //温馨提示(tvLayoutWarmPrompt)and近效期、临期商品不退换提示语位置优化
        tvTipOptimize.setVisibility(productDetail.isActivityType() || productDetail.isNearEffectiveFlag() ? View.VISIBLE : View.GONE);

        if (productDetail.isActivityType()) {
            tvTipOptimize.setText(R.string.tips_optimize_product_detail);
        }
        if (productDetail.isNearEffectiveFlag()) {
            tvTipOptimize.setText(R.string.detail_tv_warm_prompt);
        }
        if (TextUtils.isEmpty(productDetail.actPurchaseTip)){
            tvLimited.setVisibility(View.GONE);
        }else {
            tvLimited.setVisibility(View.VISIBLE);
            tvLimited.setText(productDetail.actPurchaseTip);
        }

    }

    private boolean setControlAndOEM(ProductDetailBean productDetail, boolean isUniformPrice, boolean isBuy) {
        if (TextUtils.isEmpty(productDetail.priceDesc)) {
            ivZengPinTip.setVisibility(View.GONE);
        } else {
            ivZengPinTip.setVisibility(View.VISIBLE);
            ivZengPinTip.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    AlertDialogEx alert = new AlertDialogEx(getContext());
                    alert.setTitle("价格说明")
                            .setMessage(productDetail.priceDesc)
                            .setCancelButton("我知道了", (dialog, button) -> dialog.dismiss());
                    alert.show();
                }
            });
        }
        if (productDetail.isControl == 1 && !mIsWholeSale) {

            if (productDetail.isPurchase) {
                //控销可购买
                tvControl.setVisibility(View.GONE);
                boolean isRemind = (productDetail.status == 2 || productDetail.availableQty <= 0);
                elEdit.setVisibility(isRemind ? View.INVISIBLE : View.VISIBLE);
                addRemind.setVisibility(isRemind ? View.VISIBLE : View.INVISIBLE);
                llRemind.setVisibility(isRemind ? View.VISIBLE : View.INVISIBLE);
                addCart.setVisibility(View.GONE);
                lyProductPriceKxjMl.setVisibility(isUniformPrice ? View.GONE : View.GONE);
                rlSuggestedRetailPrice02.setVisibility(isUniformPrice ? View.GONE : View.VISIBLE);
                rlSuggestedRetailPrice03.setVisibility(isUniformPrice ? View.GONE : View.VISIBLE);

                if (productDetail.priceType == 1) {
                    setFobPrice(fob);
                    tvTaxAmount.setVisibility(View.VISIBLE);
                    tvDepreciateInform.setVisibility(View.VISIBLE);
                    tvCorrection.setVisibility(View.VISIBLE);
                    tvList.setVisibility(View.GONE);
                } else {
                    //商品区间价格
                    setProductPriceRanges(productDetail);
                }

                //是否是OEM协议商品
                setIsOEM(productDetail);
            } else {
                //控销不可购买
                tvList.setVisibility(View.GONE);
                tvTaxAmount.setVisibility(View.GONE);
                tvDepreciateInform.setVisibility(View.GONE);
                tvCorrection.setVisibility(View.GONE);
                tvControl.setVisibility(View.VISIBLE);
                tvControl.setText("暂无购买权限");
                elEdit.setVisibility(View.GONE);
                addRemind.setVisibility(View.GONE);
                llRemind.setVisibility(View.GONE);
                addCart.setVisibility(View.VISIBLE);
                lyProductPriceKxjMl.setVisibility(View.GONE);//轮播图控销价毛利率
                rlSuggestedRetailPrice02.setVisibility(View.GONE);
                rlSuggestedRetailPrice03.setVisibility(View.GONE);
                rlControl.setVisibility(View.GONE);//控销价
                rlSuggestedRetailPrice.setVisibility(View.GONE);//建议零售价
                rlLimitPrice.setVisibility(View.GONE);//原价
                isBuy = false;
            }
        } else {
            //是否是OEM协议商品
            setIsOEM(productDetail);
        }

        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 文字 “签署协议后可见” 替换为商品价格；
        if (productDetail.controlType == 5) {
            setIsControlType();
        }

        // 设置单价显示
        setUnitPriceDisplay(productDetail);

        return isBuy;
    }

    private void setUniformPrice(ProductDetailBean productDetail, boolean isUniformPrice) {
        if (isUniformPrice) {
            rlSuggestedRetailPrice03.setVisibility(View.GONE);
            rlSuggestedRetailPrice02.setVisibility(View.GONE);
            lyProductPriceKxjMl.setVisibility(View.GONE);
        } else {
            rlSuggestedRetailPrice03.setVisibility(View.VISIBLE);
            rlSuggestedRetailPrice02.setVisibility(View.VISIBLE);
            lyProductPriceKxjMl.setVisibility(View.GONE);

            //建议零售价
            if (isKaUser) {//处理ka用户建议零售价和毛利展示
                tvOntrolMarket.setText(R.string.product_info_lsj_title);
                tvLayout1102.setText(R.string.product_info_lsj_title);
                tvLayout11.setText(R.string.product_info_lsj_title);

                setSuggestOrGrossMargin(productDetail.grossMargin
                        , tvProductPriceKxj, tvProductPriceMl, StringUtil.getUniformPrice2Double(productDetail.suggestPriceStr));

                String suggestedRetailPrice = ":\t" + productDetail.suggestPriceStr;
                if (!TextUtils.isEmpty(productDetail.grossMargin)) {
                    suggestedRetailPrice += "(毛利率" + productDetail.grossMargin + ")";
                }

                tvSuggestedRetailPrice02.setText(suggestedRetailPrice);
                tvSuggestedRetailPrice03.setText(suggestedRetailPrice);

            } else {

                if (!StringUtil.isEmpty(productDetail.suggestPrice) && productDetail.getSuggestPrice() > 0) {
                    tvOntrolMarket.setText(R.string.product_info_lsj_title);
                    tvLayout1102.setText(R.string.product_info_lsj_title);
                    tvLayout11.setText(R.string.product_info_lsj_title);

                    setSuggestOrGrossMargin(productDetail.grossMargin
                            , tvProductPriceKxj, tvProductPriceMl, StringUtil.getUniformPrice2Double(productDetail.suggestPrice));

                    String suggestedRetailPrice = ":\t" + productDetail.suggestPrice;
                    if (!TextUtils.isEmpty(productDetail.grossMargin)) {
                        suggestedRetailPrice += "(毛利率" + productDetail.grossMargin + ")";
                    }

                    tvSuggestedRetailPrice02.setText(suggestedRetailPrice);
                    tvSuggestedRetailPrice03.setText(suggestedRetailPrice);
                }
            }
            //控销价
            if (!StringUtil.isEmpty(productDetail.uniformPrice) && productDetail.getUniformPrice() > 0) {
                tvOntrolMarket.setText(R.string.product_info_kxj_title);
                tvLayout1102.setText(R.string.product_info_kxj_title);
                tvLayout11.setText(R.string.product_info_kxj_title);
                setSuggestOrGrossMargin(productDetail.grossMargin
                        , tvProductPriceKxj, tvProductPriceMl, StringUtil.getUniformPrice2Double(productDetail.uniformPrice));

                String suggestedRetailPrice = ":\t" + productDetail.uniformPrice;
                if (!TextUtils.isEmpty(productDetail.grossMargin)) {
                    suggestedRetailPrice += "(毛利率" + productDetail.grossMargin + ")";
                }
                tvSuggestedRetailPrice02.setText(suggestedRetailPrice);
                tvSuggestedRetailPrice03.setText(suggestedRetailPrice);
            }
        }
    }

    private void setIsAssemble(ProductDetailBean productDetail, boolean isAssemble) {
        llSpellGroupLimitedTimePremium.setVisibility(View.GONE);

        if (isAssemble) {
            if (actPtBean.assembleStatus == 0) {
                //拼团未开始
                noStartSpellGroupTip(actPtBean);
            }else if (actPtBean.assembleStatus == 1) {
                //拼团已开始
                startSpellGroupTip(productDetail, actPtBean, actPtBean.surplusTime);
                // 自动打开拼团弹框
                if (productDetail.licenseStatus != 1
                        && productDetail.licenseStatus != 5
                        && productDetail.status != 2) {
                    // 非价格资质认证可见状态下自动弹出
                    if (!actPtBean.supportSuiXinPin){
                        initShowSpellGroup(productDetail, actPtBean, false);
                    }
                }
            }

            if (productDetail.limitFullDiscountActInfo != null){
                handleSpellGroupLimitTimePremium(productDetail);
            }
        }
    }

    //处理
    private void handleSpellGroupLimitTimePremium(ProductDetailBean productDetail){

        if (mDetailBean != null && productDetail.limitFullDiscountActInfo != null){
            llSpellGroupLimitedTimePremium.setVisibility(View.VISIBLE);
            llSpellGroupRoot.setVisibility(View.GONE);
            tvAuditPassedVisible.setVisibility(View.GONE);
            llNoStartSpellGroupRoot.setVisibility(View.GONE);
            rlPriceLayout.setVisibility(View.GONE);
            rlTiming.setVisibility(View.GONE);
            tvPricePrefix.setVisibility(View.GONE);
            String content = "";
            long endTime = 0L;
            SpannableStringBuilder price = new SpannableStringBuilder();

            if (productDetail.limitFullDiscountActInfo.getText() != null){
                content = productDetail.limitFullDiscountActInfo.getText();
            }
            if(productDetail.limitFullDiscountActInfo.getEndTime() != null){
                endTime = productDetail.limitFullDiscountActInfo.getEndTime();
            }


            if (productDetail.limitFullDiscountActInfo.getLimitFullDiscount() != null){
                String showPriceStr = UiUtils.transform(productDetail.limitFullDiscountActInfo.getLimitFullDiscount());
                price.append("¥").append(showPriceStr);
                price.setSpan(new AbsoluteSizeSpan(16, true), 1, showPriceStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                price.setSpan(new AbsoluteSizeSpan(14, true), showPriceStr.length() - 2, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                if (productDetail.productUnit != null && !productDetail.productUnit.equals("")){
                    price.append("/"+ productDetail.productUnit);
                }
            }

            tvSpellLimitTimePrice.setText(price);
            if (content.isEmpty()){
                tvSpellGroupLimitContent.setVisibility(View.GONE);
            }else {
                tvSpellGroupLimitContent.setVisibility(View.VISIBLE);
                tvSpellGroupLimitContent.setText(content);
            }

            long localDiff = System.currentTimeMillis() - productDetail.limitFullDiscountActInfo.getResponseLocalTime();
            long leftTime = endTime - localDiff;
            limitTimePremiumCountDown(leftTime);
        }
    }

    /**
     * 倒计时
     *
     * @param surplusTime
     */
    private void limitTimePremiumCountDown(long surplusTime) {
        limitTimePremiumCountDownTimer = new CountDownTimer(surplusTime, 100) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (getNotNullActivity() != null && getNotNullActivity() instanceof BaseActivity) {
                    if (!getNotNullActivity().isFinishing()) {
                        // 计算剩余的小时数
                        long hours = millisUntilFinished / (1000 * 60 * 60);
                        // 计算剩余的分钟数（去掉小时部分后的毫秒数）
                        long minutes = millisUntilFinished % (1000 * 60 * 60) / (1000 * 60);
                        // 计算剩余的秒数（去掉分钟部分后的毫秒数）
                        long seconds = millisUntilFinished % (1000 * 60) / 1000;
                        // 计算剩余的百毫秒数
                        long hundredMilliseconds = millisUntilFinished % 1000 / 100;
                        String hundredMillisecondsStr = String.valueOf(hundredMilliseconds);
                        String secondsStr = "";
                        if (seconds < 10 ) {
                            secondsStr = "0"+seconds;
                        } else {
                            secondsStr = String.valueOf(seconds);
                        }

                        String minutesStr = "";
                        if (minutes < 10 ) {
                            minutesStr = "0"+minutes;
                        } else {
                            minutesStr = String.valueOf(minutes);
                        }

                        String hoursStr = "";
                        if (hours < 10 ) {
                            hoursStr = "0"+hours;
                        } else {
                            hoursStr = String.valueOf(hours);
                        }
                        tvMs.setText(hundredMillisecondsStr);
                        tvS.setText(secondsStr);
                        tvMin.setText(minutesStr);
                        tvH.setText(hoursStr);
                    }
                }
            }

            @Override
            public void onFinish() {
                tvMs.setText("0");
                tvS.setText("00");
                tvMin.setText("00");
                tvH.setText("00");
            }
        };
        limitTimePremiumCountDownTimer.start();
    }

    private void setWholeSale(boolean isWholeSale, ActPtBean actPtBean, ProductDetailBean productDetail) {
        if (isWholeSale) {
            //隐藏底部菜单分享
//            llShare.setVisibility(View.GONE);
            //显示底部抢购按钮
            addSpellGroup.setVisibility(View.VISIBLE);
            //设置底部按钮去抢购
            addSpellGroup.setText("去抢购");
            addSpellGroup.setEnabled(true);
            //隐藏纠错
            llFunction.setVisibility(View.GONE);
            //隐藏建议零售价
            rlSuggestedRetailPrice02.setVisibility(View.GONE);
            //设置价格单位颜色
            priceUnit.setTextColor(Color.parseColor("#9494A6"));
            //设置批购包邮价格
            String price = "";
            if (actPtBean.assemblePrice != null){
                price = actPtBean.assemblePrice +"";
            }
            if (price.startsWith("¥")) {
                price = price.substring(1);
            }
            SpannableString priceStr = StringUtil.setDotAfterSize(price, 25, R.color.tv_tax, 0);
            if (priceStr != null) tvTaxAmount.setText(priceStr);
            //设置划线价
//            if (actPtBean.assemblePrice < productDetail.fob) {
//                tvOriginalPrice.setVisibility(View.VISIBLE);
//                tvOriginalPrice.setText("¥" + UiUtils.transform(productDetail.fob));
//                Paint paint = tvOriginalPrice.getPaint();
//                paint.setFlags(Paint.STRIKE_THRU_TEXT_FLAG|Paint.ANTI_ALIAS_FLAG);
//                tvOriginalPrice.setCompoundDrawables(null, null, null, null);
//            }
            setRemind(productDetail);
            // 自动打开拼团弹框
            if (productDetail.licenseStatus != 1
                    && productDetail.licenseStatus != 5
                    && productDetail.status != 2) {
                // 非价格资质认证可见状态下自动弹出
                if (actPtBean.supportSuiXinPin) return;
                initShowSpellGroup(productDetail, actPtBean, false);
            }
        }
    }

    private void startSpellGroupTip(ProductDetailBean productDetail, ActPtBean actPtBean, long timeCount) {
        String productUnit = "";
        boolean isGoneSpellGroup = false;
        try {
            int subTitleNum = Integer.parseInt(actPtBean.subTitleNum);
            isGoneSpellGroup = subTitleNum < 1;
            llSpellGroupSubTitle.setVisibility(isGoneSpellGroup ? View.GONE : View.VISIBLE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (productDetail != null && productDetail.productUnit != null) {
            productUnit = productDetail.productUnit;
            productDetail.actPtBean = actPtBean;
        }
        String mPrice = "";
        if (actPtBean.assemblePrice != null){
            mPrice = actPtBean.assemblePrice + "";
        }
        String assemblePrice = "¥" + UiUtils.transform(actPtBean.isStepPrice() ? actPtBean.getMinSkuPrice() : mPrice);

//        String retailPrice = "¥" + UiUtils.transform(productDetail.fob);
        String orderNum = "/已拼" + actPtBean.orderNum + productDetail.getProductUnit();
//        String addSpellGroupStr = assemblePrice + "立即参团";
        SpannableString assemblePriceStr = StringUtil.setDotAfterSize(assemblePrice + generateUnit(productUnit, actPtBean.isStepPrice()), 25, R.color.white, 1);

        if (assemblePriceStr != null) {
            assemblePriceStr.setSpan(new AbsoluteSizeSpan(16, true), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            tvSpellGroupPrice.setText(assemblePriceStr);
        }
//        if (!TextUtils.isEmpty(retailPrice)) {
//            tvSpellGroupOriginalPrice.setVisibility(View.VISIBLE);
//            tvSpellGroupOriginalPrice.setText(retailPrice);
//            tvSpellGroupOriginalPrice.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG | Paint.ANTI_ALIAS_FLAG);
//        }
//        tvSpellGroupInitial.setText(skuStartNum);
        //进度条
//        progress.setProgress((int) actPtBean.percentage);
//        tvSpellGroupPercent.setText(actPtBean.percentage + "%");
        //轮播
        tvSpellGroupSubTitle.setText(actPtBean.subTitle);
        tvSpellGroupAlready.setText(orderNum);

        if (marqueeViewAdapter == null) {
            List<AssembleOrderList> assembleOrderList = actPtBean.assembleOrderList;
            if (assembleOrderList != null && assembleOrderList.size() > 0) {
                if (assembleOrderList.size() >= 9) {
                    assembleOrderList = assembleOrderList.subList(0, 9);
                } else if (assembleOrderList.size() >= 3) {
                    assembleOrderList = assembleOrderList.subList(0, 3);
                }
                marqueeView.setItemCount(assembleOrderList.size() >= 3 ? 3 : assembleOrderList.size());
                marqueeView.setFlippingLessCount(assembleOrderList.size() >= 9);
                marqueeView.setSingleLine(assembleOrderList.size() <= 1);
                marqueeViewAdapter = new MarqueeViewAdapter(assembleOrderList);
                marqueeView.setAdapter(marqueeViewAdapter);

            }
        }
        //去拼团按钮
        if (productDetail.status == 2) {
            addSpellGroup.setText("已抢光");
            addSpellGroup.setEnabled(false);
            addSpellGroup.setBackgroundColor(ContextCompat.getColor(getContext(), R.color.color_A9AEB7));
        } else {
            addSpellGroup.setText("立即参团");
            addSpellGroup.setEnabled(true);
        }

//        String surplusTime = DateTimeUtil.getLogDateTime(productDetail.surplusTime);
        if (timeCount > 0) {
            countDown(timeCount,isGoneSpellGroup);
        }
    }

    /**
     * 倒计时
     *
     * @param surplusTime
     */
    private void countDown(long surplusTime, boolean isGoneSpellGroup) {
        countDownTimer = new CountDownTimer(surplusTime * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (getNotNullActivity() != null && getNotNullActivity() instanceof BaseActivity) {
                    if (!getNotNullActivity().isFinishing()) {
                        int h = (int) (millisUntilFinished / 1000 / 60 / 60);
                        int m = (int) (millisUntilFinished / 1000 / 60 % 60);
                        int s = (int) (millisUntilFinished / 1000 % 60);
                        if(h < 24){
                            lySpellGroupTimeContent.setVisibility(View.VISIBLE);
                            tvSpellGroupHour.setText(h < 10 ? "0" + h : "" + h);
                            tvSpellGroupMinute.setText(m < 10 ? "0" + m : "" + m);
                            tvSpellGroupSecond.setText(s < 10 ? "0" + s : "" + s);
                        }else{
                            lySpellGroupTimeContent.setVisibility(View.GONE);
                        }
                    }
                }
            }

            @Override
            public void onFinish() {
                try {
                    spellGroupIsFinish(true,isGoneSpellGroup);
                    if (mRefreshListener != null) {
                        mRefreshListener.refreshGoodsDetail();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        spellGroupIsFinish(false,isGoneSpellGroup);
        countDownTimer.start();
    }

    /**
     * 设置拼团高毛
     */
    private void setSpellGroupGross() {
        String productUnit = "";
        if (productDetail != null && productDetail.productUnit != null) {
            productUnit = productDetail.productUnit;
            productDetail.actPtBean = actPtBean;
        }
        String suggestPrice = productDetail.suggestPrice;
        if (suggestPrice == null) {
            suggestPrice = "";
        } else if (!suggestPrice.contains(".")) {
            suggestPrice += ".00";
        } else if (suggestPrice.contains(".")) {
            String[] suggestPriceArray = suggestPrice.split("\\.");
            if (suggestPriceArray[1].length() == 1) {
                suggestPrice += "0";
            }
        }
        if (TextUtils.isEmpty(suggestPrice) || TextUtils.isEmpty(productDetail.grossMargin)) {
            tvSpellGroupGrossDes.setVisibility(View.GONE);
        } else {
            tvSpellGroupGrossDes.setVisibility(View.VISIBLE);
            tvSpellGroupGrossDes.setText("建议零售价￥" + suggestPrice + "(终端毛利率" + productDetail.grossMargin + ")");
        }
        String assemblePrice = "";
        if (productDetail.actPtBean.isStepPrice()) {
            assemblePrice = productDetail.actPtBean.getMinSkuPrice();
        } else {
            if (productDetail.actPtBean.assemblePrice != null){
                assemblePrice = productDetail.actPtBean.assemblePrice + "";
            }
        }
        if (!assemblePrice.contains(".")) {
            assemblePrice += ".00";
        }
        String[] assemblePriceArray = assemblePrice.split("\\.");
        if (assemblePriceArray[1].length() == 1) {
            assemblePrice += "0";
        }
        assemblePrice += generateUnit(productUnit, productDetail.actPtBean.isStepPrice());
//        String retailPrice = "¥" + UiUtils.transform(productDetail.fob);
//        if (!TextUtils.isEmpty(retailPrice)) {
//            tvSpellGroupPriceGrossOrigin.setVisibility(View.VISIBLE);
//            tvSpellGroupPriceGrossOrigin.setText(retailPrice);
//            tvSpellGroupPriceGrossOrigin.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG | Paint.ANTI_ALIAS_FLAG);
//        }
        assemblePriceArray = assemblePrice.split("\\.");
        SpannableStringBuilder assemblePriceStr1 = new SpannableStringBuilder("￥");
        SpannableStringBuilder assemblePriceStr2 = new SpannableStringBuilder(assemblePriceArray[0]);
        assemblePriceStr2.append(".");
        SpannableStringBuilder assemblePriceStr3 = new SpannableStringBuilder(assemblePriceArray[1]);
        assemblePriceStr1.setSpan(new AbsoluteSizeSpan(ScreenUtils.dip2px(getContext(), 15)), 0, assemblePriceStr1.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        assemblePriceStr2.setSpan(new AbsoluteSizeSpan(ScreenUtils.dip2px(getContext(), 25)), 0, assemblePriceStr2.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        assemblePriceStr3.setSpan(new AbsoluteSizeSpan(ScreenUtils.dip2px(getContext(), 15)), 0, assemblePriceStr3.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        assemblePriceStr1.append(assemblePriceStr2).append(assemblePriceStr3);
        tvSpellGroupPriceGross.setText(assemblePriceStr1);
    }

    private void spellGroupIsFinish(boolean isFinish,boolean isGoneSpellGroup) {
        if (!isFinish) {
            if (productDetail.licenseStatus == 1 || productDetail.licenseStatus == 5) {
                tvSpellGroupAptitude.setVisibility(View.VISIBLE);
                groupSpellGroupPrice.setVisibility(View.GONE);
                tvSpellGroupControl.setVisibility(View.GONE);
            } else if ((productDetail.isOEM && productDetail.signStatus != 1 && productDetail.isControlAgreement == 1 && productDetail.showAgree == 0) ||
                    (!productDetail.isOEM && productDetail.isControlAgreement == 1 && productDetail.showAgree == 0) ||
                    (productDetail.isOEM && productDetail.signStatus != 1 && productDetail.isControlAgreement == 0)
            ) {
                tvSpellGroupAptitude.setVisibility(View.GONE);
                groupSpellGroupPrice.setVisibility(View.GONE);
                tvSpellGroupControl.setVisibility(View.VISIBLE);
            } else {
                tvSpellGroupAptitude.setVisibility(View.GONE);
                tvSpellGroupControl.setVisibility(View.GONE);
                groupSpellGroupPrice.setVisibility(View.VISIBLE);
            }
            if (productDetail.highGrossGroupBuying == 1) {
                //拼团高毛
                clSpellGroup2.setVisibility(View.VISIBLE);
                clSpellGroup1.setVisibility(View.GONE);
                setSpellGroupGross();
            } else {
                clSpellGroup2.setVisibility(View.GONE);
                clSpellGroup1.setVisibility(View.VISIBLE);
            }
        }
        llSpellGroupRoot.setVisibility((isFinish||(productDetail.limitFullDiscountActInfo != null)) ? View.GONE : View.VISIBLE); //限时加补的时候要隐藏
        llSpellGroupSubTitle.setVisibility(isFinish ? View.GONE : View.VISIBLE);
        llSpellGroupSubTitle.setVisibility(isGoneSpellGroup ? View.GONE : View.VISIBLE);
        addSpellGroup.setVisibility(isFinish ? View.GONE : View.VISIBLE);

//        llDetailFl.setVisibility(isFinish ? View.VISIBLE : View.GONE);
//        llSelfShop.setVisibility(isFinish ? View.VISIBLE : View.GONE);
//        if (SpUtil.isKa()) {
//            detailLlInventory.setVisibility(View.GONE);
//        } else {
//            detailLlInventory.setVisibility(isFinish ? View.VISIBLE : View.GONE);
//        }
        rlPriceLayout.setVisibility(isFinish ? View.VISIBLE : View.GONE);
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        //  文字 “签署协议后可见” 替换为商品价格；
        if (productDetail.controlType == 5) {
            if (productDetail.licenseStatus == 1 || productDetail.licenseStatus == 5) {
                tvSpellGroupAptitude.setVisibility(View.VISIBLE);
                groupSpellGroupPrice.setVisibility(View.GONE);
                tvSpellGroupControl.setVisibility(View.GONE);
            } else {
                tvSpellGroupAptitude.setVisibility(View.GONE);
                tvSpellGroupControl.setVisibility(View.GONE);
                groupSpellGroupPrice.setVisibility(View.VISIBLE);
            }
        }
    }

    private void noStartSpellGroupTip(ActPtBean actPtBean) {
        startSpellGroupTip(productDetail, actPtBean, actPtBean.surplusTime);
        //隐藏拼团明细模块
        llSpellGroupSubTitle.setVisibility(View.GONE);
        //价格
        String productUnit = "";
        if (productDetail != null && productDetail.productUnit != null) {
            productUnit = productDetail.productUnit;
        }
        SpannableString assemblePriceStr;
        if (actPtBean.preheatShowPrice == 1) {
            String assemblePrice1 = UiUtils.transform(actPtBean.assemblePrice);
            assemblePriceStr = StringUtil.setDotAfterSize(assemblePrice1 + generateUnit(productUnit), 25, R.color.white, 0);
        } else {
            assemblePriceStr = new SpannableString("?" + generateUnit(productUnit));
            AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(ConvertUtils.dp2px(25));
            assemblePriceStr.setSpan(sizeSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        if (assemblePriceStr != null) tvSpellGroupPrice.setText(assemblePriceStr);
        //设置底部按钮无交互
        addSpellGroup.setOnClickListener(null);
        //设置开团时间
        long startTime = actPtBean.assembleStartTime;
        SimpleDateFormat format0 = new SimpleDateFormat("MM月dd日 HH:mm" + "开团");
        String time = format0.format(startTime);//这个就是把时间戳经过处理得到期望格式的时间
        addSpellGroup.setText(time);
        tvCountDownDes.setText("距离开始仅剩");
        //显示分享按钮
//        llShare.setVisibility(View.VISIBLE);
    }

    private void setPreferential(ProductDetailBean productDetail) {
        if (productDetail.status == 3 || productDetail.status == 5) {
            tvList.setVisibility(View.GONE);
            tvTaxAmount.setVisibility(View.VISIBLE);
            tvDepreciateInform.setVisibility(View.VISIBLE);
            tvCorrection.setVisibility(View.VISIBLE);
            setFobPrice(fob);
        }
    }

    private void setSellAndSoldStatus(ProductDetailBean productDetail) {
        String sellOutStr = getResources().getString(R.string.text_sell_out);
        String soldOutStr = getResources().getString(R.string.text_sold_out);
        boolean isSoldOut = productDetail.status == 2 || productDetail.status == 4 || productDetail.availableQty <= 0;
        String soldOut = "";
        if(productDetail.status == 4){
            soldOut = soldOutStr;
        } else if (productDetail.status == 2 || productDetail.availableQty <= 0) {
            soldOut = sellOutStr;
        }
        tvSoldOut.setVisibility(isSoldOut ? View.VISIBLE : View.GONE);
        tvSoldOut.setText(soldOut);
    }

    private void setPromotionTag(ProductDetailBean productDetail) {
        tvLimit.setText(productDetail.promotionTag);
        tvLimit.setVisibility(!TextUtils.isEmpty(productDetail.promotionTag) ? View.VISIBLE : View.INVISIBLE);
        tvLimit2.setVisibility(productDetail.leastPurchaseNum > 0 ? View.VISIBLE : View.GONE);
        tvLimit2.setText(productDetail.leastPurchaseNum + productDetail.productUnit + "起购");
    }

    private void ShowOemType(ProductDetailBean productDetail) {
        rlRecommend.setVisibility(View.VISIBLE);
        if (productDetail.isOemType()) {
            rlRecommend.setVisibility(View.GONE);
        }
    }

    private void setShowGive(ProductDetailBean productDetail) {
        if (productDetail.isShowGive()) {
            elEdit.setVisibility(View.GONE);
            addRemind.setVisibility(View.GONE);
            llRemind.setVisibility(View.GONE);
            addCart.setVisibility(View.VISIBLE);
            addCart.setText("赠品无法加购");
            tvDepreciateInform.setVisibility(View.GONE);
        }
    }

    private void setSeckillEndTime(SeckillBean actSk) {
        if (actSk != null) {
            // 秒杀
            setPriceLayoutVisible();
            if (actSk.status == 0) {
                // 预热
                rlTiming.setBackground(getResources().getDrawable(R.drawable.bg_detail_time_layout_pre));
                tvTime.setText("开抢时间");
                tvTime.setTextColor(getResources().getColor(R.color.color_FF2121));
                llTime.setVisibility(View.VISIBLE);
//                tvPricePrefix.setVisibility(View.GONE);
                tvPricePrefix.setBackground(getResources().getDrawable(R.drawable.bg_detail_price_prefix_seckill));
                elEdit.setVisibility(View.GONE);
                tvSeckillPre.setVisibility(View.VISIBLE);
                tvSeckillPre.setText("即将开抢");

                try {
                    long startTime = actSk.startTime;
                    end_time = DateTimeUtil.getDateTime(actSk.startTime);
                    endTime = startTime;
                    //remainTime = DateTimeUtil.getRemainTime3Detail(end_time);

                    tvDay.setText(end_time.substring(5, 7));
                    tvDotDay.setText("月");
                    tvHour.setText(end_time.substring(8, 10));
                    tvDotHour.setText("日");
                    tvMinute.setText(end_time.substring(11, 13));
                    tvSecond.setText(end_time.substring(14, 16));
                } catch (Exception e) {
                    // 防止这里时间解析出问题报错
                }
            } else if (actSk.status == 1) {
                end_time = DateTimeUtil.getDateTime(actSk.endTime);
                endTime = DateTimeUtil.getTime(end_time);
                setAutoRoll(true);
            }
            if (productDetail.status == 2) {
                setAllControlButtonGone();
                tvSeckillPre.setVisibility(View.VISIBLE);
                tvSeckillPre.setText("已抢光");
                tvSeckillPre.setEnabled(false);
                tvSeckillPre.setBackgroundColor(ContextCompat.getColor(getContext(), R.color.color_A9AEB7));
            }
        } else if (!TextUtils.isEmpty(productDetail.seckillEndTimeStr)) {
            // 直降 特价
            setPriceLayoutVisible();
            end_time = productDetail.seckillEndTimeStr;
            endTime = DateTimeUtil.getTime(end_time);
            setAutoRoll(true);
        } else {
            rlTiming.setVisibility(View.GONE);
            if (productDetail.getPromotionDetail() != null &&
                    productDetail.getPromotionDetail().getPromotion() != null &&
                    productDetail.getPromotionDetail().getPromotion().endTime != null) {

                setPriceLayoutVisible();
                end_time = productDetail.getPromotionDetail().getPromotion().endTime;
                endTime = DateTimeUtil.getTime(end_time);
                setAutoRoll(true);
            } else {
                rlTiming.setVisibility(View.GONE);
            }
        }
    }

    private void setApprovalNumber(ProductDetailBean productDetail) {
        String approvalNumber = productDetail.approvalNumber;
        if (!TextUtils.isEmpty(approvalNumber) && (
                approvalNumber.contains("国食健字") || approvalNumber.contains("卫食健字") || approvalNumber.contains("食健备") || approvalNumber.contains("国食进字") || approvalNumber.contains("卫食健进字")
        )) {
            rlProductBjpTip.setVisibility(View.VISIBLE);
        } else {
            rlProductBjpTip.setVisibility(View.GONE);
        }
    }

    private void setService(ProductDetailBean productDetail) {
        List<PromiseListBean> promiseList = productDetail.getPromiseList();
        plService.setVisibility(View.GONE);
        if (promiseList != null && promiseList.size() > 0) {
            plService.setVisibility(View.VISIBLE);
            llServiceTag.setVisibility(View.VISIBLE);
            setServiceAbstract(promiseList);
        } else {
            llServiceTag.setVisibility(View.GONE);
        }
    }

    private void setDescription(ProductDetailBean productDetail) {
        boolean isDescription = productDetail.description != null;
        String str = isDescription ? productDetail.description : "";
        int length = isDescription ? productDetail.description.length() : 0;
        String descriptionUrl = "";
        if (!TextUtils.isEmpty(productDetail.descriptions)) {
            str = str + productDetail.descriptions;
        }
        if (!TextUtils.isEmpty(productDetail.descriptionUrl)) {
            descriptionUrl = productDetail.descriptionUrl;
        }
        tvSubtitle.setText(str);
        setDescription(str, descriptionUrl, length);
        tvSubtitle.setVisibility(!TextUtils.isEmpty(str) ? View.VISIBLE : View.GONE);
    }

    private void setRemind(ProductDetailBean productDetail) {
        boolean isRemind = (productDetail.status == 2 || productDetail.status == 4 || productDetail.availableQty <= 0);
        if (isRemind) {
            addRemind.setVisibility(View.VISIBLE);
            llRemind.setVisibility(View.VISIBLE);
            elEdit.setVisibility(View.INVISIBLE);
            addSpellGroup.setVisibility(View.GONE);
            handler.sendMessage(handler.obtainMessage(REMIND_COLLECT_CODE, productDetail.favoriteFlag));
        } else {
            addRemind.setVisibility(View.INVISIBLE);
            llRemind.setVisibility(View.INVISIBLE);
            elEdit.setVisibility(View.VISIBLE);
        }
    }

    /*
     * 根据倒计时判断价格栏是否显示
     * */
    private void setPriceLayoutVisible() {
        rlTiming.setVisibility(View.VISIBLE);
        rlPriceLayout.setVisibility(View.GONE);
    }

    /**
     * 设置优先购的购买权限
     *
     * @param merchantStatus
     * @param distanceEndTime
     */
    public void setAddAndUnableStatus(int merchantStatus, long distanceEndTime) {
        int elEditVisibility = elEdit.getVisibility();
        // 是优先购用户，且剩余时间大于 0, 可以购买
        // 不是优先购用户，即使是剩余时间大于 0，也不能买
        if (merchantStatus != 1 && distanceEndTime > 0 && elEditVisibility == View.VISIBLE) {
            elEdit.setVisibility(View.INVISIBLE);
            addCart.setVisibility(View.VISIBLE);
        }
    }

    private void setFobPrice(String fob) {
        if (productDetail != null && RangePriceBeanKt.isStep(productDetail.rangePriceBean)) {
            tvTaxAmount.setText(RangePriceBeanKt.getSingleStepSpannableForCommodity(productDetail.rangePriceBean));
        } else {
            if (TextUtils.isEmpty(fob)) return;
            if (fob.startsWith("¥")) {
                fob = fob.substring(1);
            }
            SpannableString fobStr = StringUtil.setDotAfterSize(fob, 25, R.color.tv_tax, 0);
            if (fobStr != null) tvTaxAmount.setText(fobStr);
        }
    }

    /**
     *
     * @return true: 不满足签署协议条件
     */
    private Boolean isNotOEMCondition(ProductDetailBean productDetail) {
        return (!productDetail.isOEM && productDetail.isControlAgreement == 1 && productDetail.showAgree == 0) ||
                (productDetail.isOEM && productDetail.signStatus == 0 && productDetail.isControlAgreement == 1 && productDetail.showAgree == 0) ||
                (productDetail.isOEM && productDetail.signStatus == 0 && productDetail.isControlAgreement == 0);
    }

    private void setIsOEM(ProductDetailBean productDetail) {
        if (isNotOEMCondition(productDetail)) {
            setOemVisible(productDetail);
        }
    }

    private void setOemVisible(ProductDetailBean productDetail) {
        tvControl.setVisibility(View.VISIBLE);
        tvControl.setText("价格签署协议可见");
        // 温馨提示
        if (productDetail.buyTip != null) {
            tvLayoutWarmPrompt.setText(productDetail.buyTip);
        } else {
            tvLayoutWarmPrompt.setText("");
        }
        tvLayoutWarmPrompt.setVisibility(View.VISIBLE);

        tvDepreciateInform.setVisibility(View.GONE);//降价通知
        tvCorrection.setVisibility(View.GONE);
        lyProductPriceKxjMl.setVisibility(View.GONE);//控销价零售价
        rlSuggestedRetailPrice02.setVisibility(View.GONE);
        rlSuggestedRetailPrice03.setVisibility(View.GONE);
        tvList.setVisibility(View.GONE);//价格区间
        tvTaxAmount.setVisibility(View.GONE);//价格
        rlControl.setVisibility(View.GONE);//控销价
        rlSuggestedRetailPrice.setVisibility(View.GONE);//建议零售价
        rlGrossMargin.setVisibility(View.GONE);//毛利率
        rlLimitPrice.setVisibility(View.GONE);//原价

        rlTiming.setVisibility(View.GONE);//倒计时模块
        rlPriceLayout.setVisibility(View.VISIBLE);//价格模块

    }

    private void setIsControlType() {
        tvControl.setVisibility(View.GONE);

        // 战略客户专属商品:协议签署即可采取，请联系业务经理
        if (productDetail.buyTip != null) {
            tvLayoutWarmPrompt.setText(productDetail.buyTip);
        } else {
            tvLayoutWarmPrompt.setText("");
        }
        tvLayoutWarmPrompt.setVisibility(View.VISIBLE);

        tvDepreciateInform.setVisibility(View.VISIBLE);//降价通知
        tvCorrection.setVisibility(View.VISIBLE);
        lyProductPriceKxjMl.setVisibility(View.GONE);//控销价零售价 建议零售价
        rlSuggestedRetailPrice02.setVisibility(View.GONE);
        rlSuggestedRetailPrice03.setVisibility(View.GONE);
        tvList.setVisibility(View.VISIBLE);//价格区间
        tvTaxAmount.setVisibility(View.VISIBLE);//价格
        rlControl.setVisibility(View.GONE);//控销价
        rlSuggestedRetailPrice.setVisibility(View.GONE);//建议零售价
        rlGrossMargin.setVisibility(View.GONE);//毛利率
        rlLimitPrice.setVisibility(View.GONE);//原价

        rlTiming.setVisibility(View.GONE);//倒计时模块
        rlPriceLayout.setVisibility(View.VISIBLE);//价格模块
    }

    /**
     * 设置控销价或者零售价
     */
    private void setSuggestOrGrossMargin(String grossMarginStr, TextView tv_productPriceKxj, TextView tv_productPriceMl, String uniformPrice2Double) {

        try {
            //建议零售价或者控销价
            tv_productPriceKxj.setText(uniformPrice2Double);
            //毛利率
            String productPriceMl = "(毛利率" + grossMarginStr + ")";
            tv_productPriceMl.setText(productPriceMl);
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }

    /**
     * 设置显示优惠券
     */
    private void setCouponList(List<TagBean> voucherTagList) {
        rlCoupon.setVisibility((voucherTagList == null || voucherTagList.isEmpty()) ? View.GONE : View.VISIBLE);

        if (voucherTagList == null || voucherTagList.isEmpty()) {
            TagBean couponTitleBean = new TagBean();
            couponTitleBean.text = "领券";
            couponTitleBean.textColor = "#FF4741";
            couponTitleBean.bgColor = "#0DFF4741";
            couponTitleBean.borderColor = "#80FF4741";
            TextViewKt.tagStyle(tvCouponTitle, couponTitleBean);
        } else {
            tvCouponTitle.setVisibility(View.GONE);
        }

        if (voucherTagList != null && !voucherTagList.isEmpty()) {
            if (voucherTagList.get(0) != null) {
                tvCouponOne.setVisibility(View.VISIBLE);
                tvCouponTwo.setVisibility(View.GONE);
                TextViewKt.tagStyle(tvCouponOne, voucherTagList.get(0));
            }
            if (voucherTagList.size() > 1 && voucherTagList.get(1) != null) {
                tvCouponTwo.setVisibility(View.VISIBLE);
                TextViewKt.tagStyle(tvCouponTwo, voucherTagList.get(1));
            }
        }
    }

    /**
     * 设置显示促销标签
     */
    private void setShowPromotion(List<TagBean> mCxTagList) {
        if (mCxTagList == null || mCxTagList.isEmpty()) {
            llShowPromotion.setVisibility(View.GONE);
            tvCouponTitle02.setVisibility(View.GONE);
            return;
        } else {
            llShowPromotion.setVisibility(View.VISIBLE);
            tvCouponTitle02.setVisibility(View.GONE);
            if (rlCoupon.getVisibility() == View.GONE) {
                tvCouponTitle02.setVisibility(View.GONE);
            }
        }
        //mock代码
//        if(mCxTagList.size() > 0){
//            mCxTagList.get(0).csuList = new ArrayList<CSUDetailBean>();
//            mCxTagList.get(0).csuList.add(new CSUDetailBean("1safsafsafasfsdsfs", "https://files.ybm100.com/INVT/Ykq/business/picture/2022-06-06/2d9b10a3a8bf4c4487c575114f4424b7.png"
//            , "1112132131231", "sakfsafsaf", "112312", "324324234"));
//        }
        //mock代码
        CSUListView v1 = getView().findViewById(R.id.csu_01);
        CSUListView v2 = getView().findViewById(R.id.csu_02);
        CSUListView v3 = getView().findViewById(R.id.csu_03);
        v1.setVisibility(View.GONE);
        v2.setVisibility(View.GONE);
        v3.setVisibility(View.GONE);
        boolean isMainProductVirtualSupplier = productDetail.isVirtualSupplier;
        v1.setIsMainProductVirtualSupplier(isMainProductVirtualSupplier);
        v2.setIsMainProductVirtualSupplier(isMainProductVirtualSupplier);
        v3.setIsMainProductVirtualSupplier(isMainProductVirtualSupplier);
        CSUListAdapter.CSUItemEventListener listener = new CSUListAdapter.CSUItemEventListener() {
            @Override
            public void onItemClick(String skuId) {
                HashMap<String, String> m = new HashMap<>();
                m.put("productId", skuId);
                XyyIoUtil.track(PAGE_COMMODITYDETAILS_GIFTCARD_CLICK, m);
            }

            @Override
            public void onItemExposure(String skuId) {
                HashMap<String, String> m = new HashMap<>();
                m.put("productId", skuId);
                XyyIoUtil.track(PAGE_COMMODITYDETAILS_GIFTCARD_EXPOSURE, m);
            }

        };
        v1.setItemEventListener(listener);
        v2.setItemEventListener(listener);
        v3.setItemEventListener(listener);

        //设置商品
        if (mCxTagList.size() > 0 && !TextUtils.isEmpty(mCxTagList.get(0).text)) {
            ll01.setVisibility(View.VISIBLE);
            ll02.setVisibility(View.GONE);
            ll03.setVisibility(View.GONE);
            //createView(mCxTagList.get(0), tvIconType01, ivIconType01);
            TextViewKt.tagStyle(tvIconType01, mCxTagList.get(0));
            tvContentType01.setText(mCxTagList.get(0).description);

            if (mCxTagList.get(0).csuList != null && !mCxTagList.get(0).csuList.isEmpty()) {
                v1.setVisibility(View.VISIBLE);
                v1.setListData(mCxTagList.get(0).csuList, mCxTagList.get(0), true);
            }
        }
        if (mCxTagList.size() > 1 && !TextUtils.isEmpty(mCxTagList.get(1).text)) {
            ll02.setVisibility(View.VISIBLE);
            ll03.setVisibility(View.GONE);
//            createView(mCxTagList.get(1), tvIconType02, ivIconType02);
            TextViewKt.tagStyle(tvIconType02, mCxTagList.get(1));
            tvContentType02.setText(mCxTagList.get(1).description);
            if (mCxTagList.get(1).csuList != null && !mCxTagList.get(1).csuList.isEmpty()) {
                v2.setVisibility(View.VISIBLE);
                v2.setListData(mCxTagList.get(1).csuList, mCxTagList.get(1), true);
            }
        }
        if (mCxTagList.size() > 2 && !TextUtils.isEmpty(mCxTagList.get(2).text)) {
            ll03.setVisibility(View.VISIBLE);
            //createView(mCxTagList.get(2), tvIconType03, ivIconType03);
            TextViewKt.tagStyle(tvIconType03, mCxTagList.get(2));
            tvContentType03.setText(mCxTagList.get(2).description);
            if (mCxTagList.get(2).csuList != null && !mCxTagList.get(2).csuList.isEmpty()) {
                v3.setVisibility(View.VISIBLE);
                v3.setListData(mCxTagList.get(2).csuList, mCxTagList.get(2), true);
            }
        }

    }

    private void setShowSpellGroup(SkuDetailPromotionInfo skuDetailPromotionInfo, boolean isShow) {
        if (productDetail.tags != null) {
            if (productDetail.tags.orderFreeShippingTag != null) {
                llNewTag.setVisibility(View.VISIBLE);
                clNewTag.setVisibility(View.VISIBLE);
                TextViewKt.tagStyle(tvNewTag1, productDetail.tags.orderFreeShippingTag);
                tvNewTag1.setText(productDetail.tags.orderFreeShippingTag.text);
                tvNewTag2.setText(productDetail.tags.orderFreeShippingTag.description);
                if (isShow) {
                    rlCouponOrPromotion.setVisibility(View.VISIBLE);
                    tvCouponTitle02.setVisibility(View.GONE);
                }
            }
        }
        if (skuDetailPromotionInfo.getNotUseCouponTag() != null) {
            llNewTag.setVisibility(View.VISIBLE);
            tvNewTag3.setVisibility(View.VISIBLE);
            TextViewKt.tagStyle(tvNewTag3, skuDetailPromotionInfo.getNotUseCouponTag());
            if (isShow) {
                rlCouponOrPromotion.setVisibility(View.VISIBLE);
                tvCouponTitle02.setVisibility(View.GONE);
            }
        }
        if (productDetail.tags == null && skuDetailPromotionInfo.getNotUseCouponTag() == null) {
            llNewTag.setVisibility(View.GONE);
            clNewTag.setVisibility(View.GONE);
        }
    }

    //ivIconType01s
    private void createView(LabelIconBean bean, TextView textView, ImageView imageView) {
        textView.setPadding(ConvertUtils.dp2px(3), ConvertUtils.dp2px(1.7f), ConvertUtils.dp2px(3), ConvertUtils.dp2px(1.5f));
        int drawRes = R.drawable.bg_brand_item_type4;
        int colorRes = R.color.brand_icon_type4;
        if (bean != null) {
            textView.setText(bean.name);
            switch (bean.uiType) {
                //标签显示类型 1,临期 2，券 3，自定义1 4，自定义2
                case 1:
                    drawRes = R.drawable.bg_brand_item_type1;
                    colorRes = R.color.white;
                    break;
                case 2:
                    drawRes = R.drawable.shape_tag_bg_red;
                    colorRes = R.color.color_ff2121;
                    break;
                case 3:
                    drawRes = R.drawable.bg_brand_item_type3;
                    colorRes = R.color.brand_icon_type3;
                    break;
                case 5://医保
                    drawRes = R.drawable.bg_brand_item_health_insurance;
                    colorRes = R.color.white;
                    break;
                case 999:
                    textView.setVisibility(View.GONE);
                    imageView.setVisibility(View.VISIBLE);
                    String url = "";
                    if (!bean.name.startsWith("http")) {
                        url = AppNetConfig.LORD_TAG + bean.name;
                    }
                    ImageHelper.with(getNotNullActivity()).load(url)
                            .placeholder(R.drawable.transparent)
                            .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                            .dontAnimate().dontTransform().into(imageView);
                    break;
                case 4:
                default:
                    drawRes = R.drawable.bg_brand_item_type4;
                    colorRes = R.color.brand_icon_type4;
                    break;

            }
        } else {
            textView.setVisibility(View.INVISIBLE);
        }
        textView.setBackgroundResource(drawRes);
        textView.setTextColor(getResources().getColor(colorRes));
    }

    /**
     * 设置商品描述-点击跳转url
     *
     * @param str    商品描述文案
     * @param url    跳转链接
     * @param length 长度，从length到spanText.length()开始显示下划线
     */
    public void setDescription(String str, final String url, int length) {
        if (TextUtils.isEmpty(str) || str.length() == length) {
            return;
        }
        try {
            SpannableString spanText = new SpannableString(str);
            spanText.setSpan(new ClickableSpan() {

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    //设置文件颜色
                    ds.setColor(UiUtils.getColor(R.color.detail_tv_FF982C));
                    //设置下划线
                    ds.setUnderlineText(false);
                }

                @Override
                public void onClick(@NonNull View view) {
                    if (!TextUtils.isEmpty(url)) {
                        RoutersUtils.open(url);
                    }
                }
            }, length, spanText.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            //设置点击后的颜色为透明，否则会一直出现高亮
            tvSubtitle.setHighlightColor(Color.TRANSPARENT);
            tvSubtitle.setText(spanText);
            //开始响应点击事件
            tvSubtitle.setMovementMethod(LinkMovementMethod.getInstance());
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }

    }

    /**
     * 设置价格区间
     *
     * @param productDetail 商品详情-实体类
     */
    private void setProductPriceRanges(ProductDetailBean productDetail) {
        if (productDetail.getSkuPriceRangeList() != null
                && productDetail.getSkuPriceRangeList().size() > 0) {
            tvList.setVisibility(View.VISIBLE);
            tvTaxAmount.setVisibility(View.GONE);
            tvDepreciateInform.setVisibility(View.VISIBLE);
            tvCorrection.setVisibility(View.VISIBLE);
            tvControl.setVisibility(View.GONE);
            setPriceRange(productDetail.getSkuPriceRangeList());
        } else {
            String fob = "¥" + UiUtils.transform(productDetail.fob);
            tvList.setVisibility(View.GONE);
            tvTaxAmount.setVisibility(View.VISIBLE);
            tvDepreciateInform.setVisibility(View.VISIBLE);
            tvCorrection.setVisibility(View.VISIBLE);
            setFobPrice(fob);
        }


    }

    /**
     * 药品信息
     *
     * @param productDetail 商品详情-实体类
     */
    public void getDrugInformation(final ProductDetailBean productDetail) {

        //运费提示
        rlFreightTips.setVisibility(TextUtils.isEmpty(productDetail.freightTips) ? View.GONE : View.VISIBLE);
        tvFreightTips.setText(productDetail.freightTips);

        rlLetterPackage.setVisibility(TextUtils.isEmpty(productDetail.pieceLoading) ? View.GONE : View.VISIBLE);
        rlSpec.setVisibility(TextUtils.isEmpty(productDetail.spec) ? View.GONE : View.VISIBLE);
        rlMediumPackage.setVisibility(TextUtils.isEmpty(productDetail.mediumPackage) ? View.GONE : View.VISIBLE);
        rlSuggestedRetailPrice.setVisibility(View.GONE);
        rlManufacturer.setVisibility(TextUtils.isEmpty(productDetail.manufacturer) ? View.GONE : View.VISIBLE);
        rlApprovalNumber.setVisibility(TextUtils.isEmpty(productDetail.approvalNumber) ? View.GONE : View.VISIBLE);
        //12-1jianghongbo 产品要求隐藏该段     rlGrossMargin.setVisibility(TextUtils.isEmpty(productDetail.grossMargin) ? View.GONE : View.VISIBLE);
        rlControl.setVisibility(TextUtils.isEmpty(productDetail.uniformPrice) ? View.GONE : View.VISIBLE);
        tvPossibleToDisassemble.setVisibility(TextUtils.isEmpty(productDetail.isSplitTitle) ? View.GONE : View.VISIBLE);

        //医保代码
        tvMedicalInsuranceCode.setVisibility(TextUtils.isEmpty(productDetail.cnProductCode) ? View.GONE : View.VISIBLE);
        medicalInsuranceCodeContent.setText(productDetail.cnProductCode);
        tvMiCopy.setOnClickListener(new View.OnClickListener()  {
            @Override
            public void onClick(View v) {
                ClipboardManager cm = (ClipboardManager) v.getContext().getSystemService(Context.CLIPBOARD_SERVICE);
                cm.setText(medicalInsuranceCodeContent.getText());
                ToastUtils.showShort("已经复制");
            }
        });

        //产地
        rlProducer.setVisibility(TextUtils.isEmpty(productDetail.producer) ? View.GONE : View.VISIBLE);
        tvProducerContent.setText(productDetail.producer);


        tvSpec.setText(productDetail.spec);
        tvMediumPackage.setText(productDetail.mediumPackage);
        tvPossibleToDisassemble.setText(productDetail.isSplitTitle);
        String suggestedRetailPrice = "¥\t" + productDetail.suggestPrice + "(毛利率" + productDetail.grossMargin + ")";
        tvSuggestedRetailPrice.setText(suggestedRetailPrice);
        tvManufacturer.setText(productDetail.manufacturer);
        //器械类商品
        if (TextUtils.equals("100005", productDetail.categoryFirstId)) {
            tvLayout06.setVisibility(View.GONE);
            tvApprovalNumber.setVisibility(View.GONE);
            tvApprovalNumber2.setText("医疗器械注册证或备案凭证编号: " + productDetail.approvalNumber);
            tvApprovalNumber2.setVisibility(View.VISIBLE);
        } else {
            tvApprovalNumber.setText(productDetail.approvalNumber);
        }
        tvLetterPackage.setText(productDetail.pieceLoading);
        //tvExpirationDate.setText(productDetail.shelfLife);
        tvGrossMargin.setText(StringUtil.getGrossMargin2Double(productDetail.grossMargin));
        tvControlPrice.setText(StringUtil.getUniformPrice2Double(productDetail.uniformPrice));

        // 医保编码
        if (!TextUtils.isEmpty(productDetail.medicalInsuranceCode)) {
            rlHealthCareCode.setVisibility(View.VISIBLE);
            tvHealthCareCode.setText(productDetail.medicalInsuranceCode);
        }

        // 近/远效期
        if (TextUtils.isEmpty(productDetail.effectStr)) {
            rlValidityLayout.setVisibility(View.GONE);
        } else {
            tvValidity.setText(productDetail.effectStr);
        }

        // 生产日期
        if (TextUtils.isEmpty(productDetail.manufactureDate)) {
            rlDateOfManufacture.setVisibility(View.GONE);
        } else {
            rlDateOfManufacture.setVisibility(View.VISIBLE);
            tvDateOfManufactureContent.setText(productDetail.manufactureDate);
        }

//        ViewTreeObserver vto = tvValidity.getViewTreeObserver();
//        vto.addOnGlobalLayoutListener(() -> {
//            if (tvValidity == null) {
//                return;
//            }
//            try {
//                TextPaint mTextPaint = tvValidity.getPaint();
//                mTextPaint.setTextSize(tvValidity.getTextSize());
//                int mTextViewWidth = (int) mTextPaint.measureText(TextUtils.isEmpty(productDetail.effectStr) ? "" : productDetail.effectStr);
//                //超出一行
//                if (tvValidity.getWidth() > 0 && mTextViewWidth > tvValidity.getWidth()) {
//                    rlValidityGone.setVisibility(View.VISIBLE);
//                    tvValidity.setText(productDetail.nearEffect);
//                    tvValidityGone.setText(productDetail.farEffect);
//                } else {
//                    rlValidityGone.setVisibility(View.GONE);
//                }
//            } catch (Exception e) {
//                BugUtil.sendBug(e);
//            }
//        });
        if (productDetail.nextDayServiceTagList != null && !productDetail.nextDayServiceTagList.isEmpty()) {
            rlNextDay.setVisibility(View.VISIBLE);
            NextDayServiceTagListBean bean = productDetail.nextDayServiceTagList.get(0);
            tvNextDes.setText(bean.getDescription());
            int textColorInt = Color.parseColor("#00B377");
            try {
                textColorInt = Color.parseColor(bean.getTextColor());
            } catch (Exception e) {
            }
            tvNextLabel.setTextColor(textColorInt);
            int bgColorInt = Color.parseColor("#04BE88");
            try {
                Color.parseColor(bean.getBgColor());
            } catch (Exception e) {
            }

            int borderColorInt = Color.parseColor("#04BE88");
            try {
                borderColorInt = Color.parseColor(bean.getBorderColor());
            } catch (Exception e) {
            }

            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setColor(bgColorInt);
            gradientDrawable.setCornerRadius(UiUtils.dp2px(2));
            gradientDrawable.setStroke(UiUtils.dp2px(1), borderColorInt);
            tvNextLabel.setBackground(gradientDrawable);
            tvNextLabel.setText(bean.getName());
            if (!TextUtils.isEmpty(bean != null ? bean.getAppIcon() : null)) {
                Glide.with(getContext())
                        .load(AppNetConfig.getCDNHost() + (bean != null ? bean.getAppIcon() : null)) // bean.imageUrl 是网络图片的 URL
                        .into(new SimpleTarget<GlideDrawable>() {
                            @Override
                            public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                                // 设置 drawableStart
                                resource.setBounds(0, 0,UiUtils.dp2px(7), UiUtils.dp2px(10));
                                tvNextLabel.setCompoundDrawablesRelative(
                                        resource,
                                        null,
                                        null,
                                        null
                                );
                            }
                        });
            }
        } else {
            rlNextDay.setVisibility(View.GONE);
        }
    }


    /**
     * 设置医保的tag
     * @param productDetail
     * */
    private void setTvSubtags(ProductDetailBean productDetail) {
        if (productDetail.firstTagList != null && !productDetail.firstTagList.isEmpty()) {
            tvSubtags.setVisibility(View.VISIBLE);

            // 创建一个容器来放置所有标签
            LinearLayout tagContainer = new LinearLayout(requireContext());
            tagContainer.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            tagContainer.setOrientation(LinearLayout.HORIZONTAL);

            // 循环productDetail.firstTagList
            for (LabelIconBean tagBean : productDetail.firstTagList) {
                if (tagBean.uiStyle == 3) {
                    // 如果uiStyle值为3，则创建图片
                    ImageView imageView = new ImageView(requireContext());
                    // 设置imageView的尺寸为宽度：36.5dp,高度为：11.5dp
                    int widthInDp = 30;
                    int heightInDp = 14;
                    int widthInPx = UiUtils.dp2px(widthInDp);
                    int heightInPx = UiUtils.dp2px(heightInDp);
                    LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(widthInPx, heightInPx);
                    // 设置图片的显示方式
                    imageView.setScaleType(ImageView.ScaleType.FIT_XY); // 或其他适合的ScaleType
                    // 添加右边距
                    layoutParams.rightMargin = UiUtils.dp2px(6);
                    imageView.setLayoutParams(layoutParams);

                    // 加载图片
                    if (!TextUtils.isEmpty(tagBean.appIcon)) {
                        String url = tagBean.appIcon;
                        if (!url.startsWith("http")) {
                            url = AppNetConfig.LORD_TAG + url;
                        }

                        ImageHelper.with(requireContext())
                                .load(url)
                                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                                .dontAnimate()
                                .into(imageView);
                    }

                    tagContainer.addView(imageView);
                } else {
                    // 否则用ShopNameWithTagView创建
                    List<TagBean> tagList = new ArrayList<>();
                    // 将ProductDetailTagsBean转换为TagBean
                    TagBean tag = new TagBean();
                    tag.text = tagBean.text;
                    tag.textColor = tagBean.textColor;
                    tag.bgColor = tagBean.bgColor;
                    tag.borderColor = tagBean.borderColor;
                    tag.appIcon = tagBean.appIcon;
                    try {
                        tag.uiStyle = tagBean.uiStyle;
                    } catch (NumberFormatException e) {
                        tag.uiStyle = 1; // 默认值
                    }
                    tagList.add(tag);

                    ShopNameWithTagView tagView = new ShopNameWithTagView(requireContext());
                    tagView.bindData(tagList, null, 100);

                    // 添加右边距
                    LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                            LinearLayout.LayoutParams.WRAP_CONTENT,
                            LinearLayout.LayoutParams.WRAP_CONTENT);
                    layoutParams.rightMargin = UiUtils.dp2px(4);
                    tagView.setLayoutParams(layoutParams);

                    tagContainer.addView(tagView);
                }
            }

            // 最后加入到tvSubtags中
            tvSubtags.addView(tagContainer);
        } else {
            tvSubtags.setVisibility(View.GONE);
        }
    }


    /**
     * 设置中药属性、商品信息增加中药扩展
     * @param productDetail
     */
    private void setCnMedineAttr(ProductDetailBean productDetail){
        // 顶部横条
        if(productDetail.keyAttributeList != null && !productDetail.keyAttributeList.isEmpty()){
            fyCnAttr.setVisibility(View.VISIBLE);
            rlSpec.setVisibility(View.GONE);    // 隐藏原规格UI
            commodityKeyAttrAdapter = new ProductDetailCnMedineAttrAdapter(productDetail.keyAttributeList);
            rlvCnAttr.setAdapter(commodityKeyAttrAdapter);
            rlvCnAttr.getViewTreeObserver().addOnGlobalLayoutListener(cnMedineGlobalListener);
        }else{
            fyCnAttr.setVisibility(View.GONE);
            rlSpec.setVisibility(View.VISIBLE);
        }
        // 商品信息生产日期后追加 重要扩展属性
        if(productDetail.skuExtAttrDTOS != null && !productDetail.skuExtAttrDTOS.isEmpty()){
            // 商品信息添加扩展属性
            lyCnMedineSkuExt.setVisibility(View.VISIBLE);
            for (ProductDetailKeyAttr data :
                    productDetail.skuExtAttrDTOS) {
                ProductDetailCnmExtLayout item = new ProductDetailCnmExtLayout(requireContext());
                item.setItemData(data);
                ViewGroup.MarginLayoutParams lp = new ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                lp.topMargin = DensityUtil.dip2px(requireContext(),10f);
                item.setLayoutParams(lp);
                lyCnMedineSkuExt.addView(item);
            }
        }else{
            lyCnMedineSkuExt.setVisibility(View.GONE);
        }
    }
    /**
     * 设置组合购、加价购数据
     */
    private void setCombinedBuyData(){
        int type = showGroupPurchaseFlag? 1 : showAdditionalPurchaseFlag? 2 : 0;
        if(type == 0){
            layoutCombined.setVisibility(View.GONE);
            return;
        }
        //V12.0.10 新增组合购、加价购
        mCommodityViewModel.getCombinedProductLiveData().observe(this, t -> {
            if(t.data != null && t.data.cardInfo != null) {
                if(null != t.data.cardInfo.getGroupPurchaseInfo()){
                    markGroupCombinationGoods(t.data.cardInfo.getGroupPurchaseInfo(), true, t.data.scmId);
                    if(combinedBuySingleLayout == null){
                        layoutCombined.setVisibility(View.VISIBLE);
                        combinedBuySingleLayout = new CombinedBuySingleLayout(getContext(),null);
                        layoutCombined.addView(combinedBuySingleLayout);
                    }
                    mGroupPurchaseInfo = t.data.cardInfo.getGroupPurchaseInfo();
                    combinedBuySingleLayout.setNewData(mGroupPurchaseInfo);
                    GroupPurchaseInfoKt.initGroup(mGroupPurchaseInfo);
                    AdapterUtils.INSTANCE.getGroupPurchaseInfo(mGroupPurchaseInfo,0,null,true,-1,true,groupPurchaseInfo -> {
                        if(combinedBuySingleLayout.isAttachedToWindow()){
                            combinedBuySingleLayout.setNewData(groupPurchaseInfo);
                        }
                        return null;
                    });
                    combinedBuySingleLayout.setMListener(combinedBuyListener);
                }else if(null != t.data.cardInfo.getAdditionalPurchaseInfo()){
                    markGroupCombinationGoods(t.data.cardInfo.getAdditionalPurchaseInfo(), false, t.data.scmId);
                    if(combinedBuyMultiLayout == null){
                        layoutCombined.setVisibility(View.VISIBLE);
                        combinedBuyMultiLayout = new CombinedBuyMultiLayout(getContext());
                        layoutCombined.addView(combinedBuyMultiLayout);
                    }
                    mAddPurchaseInfo = t.data.cardInfo.getAdditionalPurchaseInfo();
                    GroupPurchaseInfoKt.initAdditonal(mAddPurchaseInfo);
                    AdapterUtils.INSTANCE.getAdditionalPurchaseInfo(mAddPurchaseInfo,null,true,-1,true,groupPurchaseInfo -> {
                        if(combinedBuyMultiLayout.isAttachedToWindow()) {
                            combinedBuyMultiLayout.setNewData(groupPurchaseInfo);
                        }
                        return null;
                    });
                    combinedBuyMultiLayout.setNewData(mAddPurchaseInfo);
                    combinedBuyMultiLayout.setMListener(combinedBuyListener);
                }
            }
        });
        String skuId = getArguments().getString("skuId", "");
        mCommodityViewModel.getCombinedProducts(String.valueOf(skuId),type);
    }

    private CombinedBuyListener combinedBuyListener = new CombinedBuyListener() {
        @Override
        public void jumpToGoodsDetail(@NotNull RowsBeanCombinedExt bean) {
            XyyIoUtil.track("Test_Product_Action", bean);
//            String url = String.format("ybmpage://productdetail?%s=%s&nsid=%s&sdata=%s&sourceType=%s&search_sort_strategy_id=%s",
//                    IntentCanst.PRODUCTID,bean.getId(),!TextUtils.isEmpty(bean.nsid) ? bean.nsid: "\\",
//                    !TextUtils.isEmpty(bean.sdata) ? bean.sdata: "\\",bean.sourceType,
//                    bean.searchSortStrategyCode);

            Bundle mParams =new Bundle();
            mParams.putString(IntentCanst.PRODUCTID, String.valueOf(bean.getId()));
            mParams.putString("nsid", bean.nsid);
            mParams.putString("sdata", bean.sdata);
            mParams.putString("sourceType", bean.sourceType);
            mParams.putString("search_sort_strategy_id", bean.searchSortStrategyCode);
            if(bean.isMainProduct()){
                mParams.putBoolean(SHOW_GROUPPURCHASE_FLAG, false);
            }else{
                mParams.putBoolean(SHOW_GROUPPURCHASE_FLAG, bean.showGroupPurchase());
            }
            mParams.putBoolean(IntentCanst.SHOW_ADDITIONALPURCHASE_FLAG, bean.showAdditinalPurchase());
            ReportPDExtendOuterBean report = new ReportPDExtendOuterBean();
            report.setKeyWord(bean.searchKeyword);
            report.setSearchSortStrategyId(bean.searchSortStrategyCode);
            report.setOperationId(bean.operationId);
            report.setListPositionType(String.valueOf(bean.positionType));
            report.setListPositionTypename(bean.positionTypeName);
            mParams.putSerializable(IntentCanst.JG_JSON_REPORT_PD_EXTEND_OUTER_BEAN, report);
            //这里带参太多了 只能Intent跳了 不能用路由
            Intent intent =new Intent(requireActivity(), ProductDetailActivity.class);
            intent.putExtras(mParams);
            requireActivity().startActivity(intent);
            SearchProductReport.trackSearchGoodsClick(getContext(), -1, bean);
        }

        @Override
        public void refresh(RowsBeanCombinedExt preItem,int preSubPosition,RowsBeanCombinedExt item,int subPosition) {
            if(mGroupPurchaseInfo != null){
                GoodsPlaceExposureRecord.get(getContext()).clearRecordByKey(preItem.getGroupGoodsPlaceInfo().getRecordKey(getContext()));
                AdapterUtils.INSTANCE.checkCombinedProductNum(mGroupPurchaseInfo,item, subPosition, null,-1,groupPurchaseInfo -> {
                    if(combinedBuySingleLayout.isAttachedToWindow()) {
                        combinedBuySingleLayout.setNewData(groupPurchaseInfo);
                    }
                    return null;
                });
            }
        }

        @Override
        public void preSettle(int subPosition) {
            if(mGroupPurchaseInfo != null){
                SearchProductReport.trackSearchPlaceOrderClick(getContext(), mGroupPurchaseInfo.getMainProduct(), CollectionsKt.listOf(mGroupPurchaseInfo.getSubProducts().get(subPosition)), false);
                AdapterUtils.INSTANCE.preSettle(requireContext(), mGroupPurchaseInfo,subPosition);
            }else if(mAddPurchaseInfo != null){
                SearchProductReport.trackSearchPlaceOrderClick(getContext(), mAddPurchaseInfo.getMainProduct(), mAddPurchaseInfo.getSubProducts(), false);
                AdapterUtils.INSTANCE.preSettle(requireContext(), mAddPurchaseInfo,-1);
            }
        }

        @Override
        public void changeNum(@NotNull RowsBeanCombinedExt bean,int curSubPos,boolean addFlag,int preNum) {
            if(mGroupPurchaseInfo != null){
                AdapterUtils.INSTANCE.checkCombinedProductNum(mGroupPurchaseInfo,bean,curSubPos,null,-1,groupPurchaseInfo -> {
                    if(combinedBuySingleLayout.isAttachedToWindow()) {
                        combinedBuySingleLayout.setNewData(groupPurchaseInfo);
                    }
                    return null;
                });
            }else if(mAddPurchaseInfo != null){
                if(bean.getNewQty() == 0){
                    AdapterUtils.INSTANCE.getAdditionalPurchaseInfo(mAddPurchaseInfo,null,true,-1,false,groupPurchaseInfo -> {
                        if(combinedBuyMultiLayout.isAttachedToWindow()) {
                            combinedBuyMultiLayout.setNewData(groupPurchaseInfo);
                        }
                        return null;
                    });
                }else{
                    AdapterUtils.INSTANCE.checkCombinedProductNum(mAddPurchaseInfo,bean,curSubPos,null,-1,groupPurchaseInfo -> {
                        if(combinedBuyMultiLayout.isAttachedToWindow()) {
                            combinedBuyMultiLayout.setNewData(groupPurchaseInfo);
                        }
                        return null;
                    });
                }
            }
        }

        @Override
        public void changeNumClick(@NonNull RowsBeanCombinedExt bean, int curSubPos,boolean addFlag, int preNum) {
            trackCombinationBtnClick(bean, curSubPos, addFlag, preNum);
        }
    };

    private RecyclerView.OnScrollListener cnMedineScrollListener = new RecyclerView.OnScrollListener(){
        @Override
        public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
            LinearLayoutManager lm = (LinearLayoutManager) recyclerView.getLayoutManager();
            int lastComPos = lm.findLastVisibleItemPosition();
            int last = commodityKeyAttrAdapter.getData().size() - 1;
            if(lastComPos == last){
                View view = lm.findViewByPosition(last);
                view.setPadding(0,DensityUtil.dip2px(getActivity(),12f),DensityUtil.dip2px(getActivity(),30f),DensityUtil.dip2px(getActivity(),10f));
            }
        }
    };

    private ViewTreeObserver.OnGlobalLayoutListener cnMedineGlobalListener = new ViewTreeObserver.OnGlobalLayoutListener(){
        @Override
        public void onGlobalLayout() {
            if(getActivity() != null && isAdded() && !isRemoving()){
                rlvCnAttr.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                LinearLayoutManager lm = (LinearLayoutManager) rlvCnAttr.getLayoutManager();
                int lastComPos = lm.findLastCompletelyVisibleItemPosition();    // 当且可视最后一个item下标
                int last = commodityKeyAttrAdapter.getItemCount() - 1;
                // 最后一个未显示出来，需要滑动
                if(lastComPos < last){
                    rlvCnAttr.addOnScrollListener(cnMedineScrollListener);
                    ivCnAttrArrow.setVisibility(View.VISIBLE);
                }else{
                    ivCnAttrArrow.setVisibility(View.GONE);
                }
            }
        }
    };

    /**
     * 处理价格前缀
     *
     * @param productDetail
     * @param isAssemble
     */
    private void processPricePrefix(ProductDetailBean productDetail, boolean isAssemble, boolean isWholeSale) {
        if (!TextUtils.isEmpty(productDetail.pricePrefix)) {
            tvPricePrefix.setVisibility(View.VISIBLE);
            tvPricePrefix.setText(productDetail.pricePrefix);
        } else {
            tvPricePrefix.setVisibility(View.GONE);
        }

        // 如果是秒杀
        if (!TextUtils.isEmpty(productDetail.seckillEndTimeStr)) {
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) tvPricePrefix.getLayoutParams();
            layoutParams.bottomToTop = R.id.rl_timing;
            tvPricePrefix.setLayoutParams(layoutParams);
        }
        // 如果是拼购
        if (isAssemble || isWholeSale) {
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) tvPricePrefix.getLayoutParams();
            layoutParams.bottomToTop = R.id.ll_spell_group_root;
            tvPricePrefix.setLayoutParams(layoutParams);
        }


    }

    /**
     * 价格区间
     *
     * @param priceRangeList 价格区间list
     */
    private void setPriceRange(List<PriceRangeListBean> priceRangeList) {

        tvList.setSelector((new ColorDrawable(Color.TRANSPARENT)));
        if (rangeAdapter == null) {
            rangeAdapter = new PriceRangeAdapter(priceRangeList, getNotNullActivity());
            tvList.setAdapter(rangeAdapter);
        }
    }

    /**
     * 服务简介
     *
     * @param listBeen 服务简介list
     */
    private void setServiceAbstract(List<PromiseListBean> listBeen) {
        if (listBeen == null || listBeen.size() <= 0) {
            return;
        }

        listBeen = listBeen.size() > 3 ? listBeen.subList(0, 3) : listBeen;
        plService.setSelector((new ColorDrawable(Color.TRANSPARENT)));
        if (mDetailServiceAdapter == null) {
            mDetailServiceAdapter = new DetailServiceAdapter(listBeen, getNotNullActivity());
            plService.setAdapter(mDetailServiceAdapter);
            mDetailServiceAdapter.setOnItemClickListener(rows -> setServiceAbstract());
        }
    }

    /**
     * 视频-轮播
     *
     * @param items 商品详情-视频list
     */
    public void setListData(List<ImagesVideosListBean> items) {
        final List<ImagesVideosListBean> imagesVideosListBeen = new ArrayList<>();
        brandIv.setTagBg(productDetail.activityTag);
        if (items != null && items.size() > 0) {
            for (ImagesVideosListBean imagesVideosListBean : items) {
                ImagesVideosListBean videosListBean = new ImagesVideosListBean();
                if (imagesVideosListBean.type == 2) {
                    if (!TextUtils.isEmpty(imagesVideosListBean.videoUrl)) {
                        videosListBean.imageUrl = imagesVideosListBean.videoUrl;
                        videosListBean.videoUrl = imagesVideosListBean.videoUrl;
                    }
                } else {
                    videosListBean.imageUrl = imagesVideosListBean.imageUrl;
                }
                videosListBean.type = imagesVideosListBean.type;
                imagesVideosListBeen.add(videosListBean);
            }
            brandIv.setItemDataInVideo(imagesVideosListBeen, productDetail.markerUrl, productDetail);
        }
    }

    @OnClick({R.id.detail_ll_cart, R.id.iv_service, R.id.ll_show_promotion, R.id.rl_coupon, R.id.view_promotion_more, R.id.iv_promotion_more
            , R.id.rl_third_party, R.id.tv_depreciate_inform, R.id.add_remind
            , R.id.ll_on_line_service, R.id.ll_company_name, R.id.tv_correction
            , R.id.tv_audit_no_passed, R.id.rt_self_shop_open, R.id.detail_ll_self_shop
            , R.id.add_spell_group, R.id.tv_original_price, R.id.tv_tax_discount_price, R.id.tv_spell_group_original_price, R.id.ll_share})
    public void clickTab(View view) {

        switch (view.getId()) {

            case R.id.detail_ll_cart:
                // 跳转到采购单
//                Intent intent = new Intent(getNotNullActivity(), MainActivity.class);
//                intent.putExtra("comment", "2");
//                startActivity(intent);
                CommodityDetailReport.bottomBtnClickCart(requireActivity());
                if (productDetail == null) return;
                StringBuilder rawurl = new StringBuilder("ybmpage://productdetail?product_id=");
                rawurl.append(productDetail.id);
                RoutersUtils.open("ybmpage://main?tab=2&name= " + "&id=" + RoutersUtils.encodeRAWUrl(rawurl.toString()));
                break;
            case R.id.iv_service:
                setServiceAbstract();
                break;
            //促销+优惠券信息
            case R.id.view_promotion_more:
            case R.id.iv_promotion_more:
                if (jgTrackBean != null) {
                    JGTrackTopLevelKt.jgTrackProductDetailBtnClick(getActivity(), "查看优惠", jgTrackBean.getJgReferrer());
                }
                initShowCoupon();
                break;
            //第三方厂家
            case R.id.rl_third_party:
                String routerUrl = getRouterUrl(productDetail.companyName, productDetail.orgId);
                RoutersUtils.open(routerUrl);
                break;
            //降价通知
            case R.id.tv_depreciate_inform:
                if (brandIv == null || productDetail == null) {
                    return;
                }
                getAction(productDetail.fob, productDetail.id);
                break;
            //订阅提醒
            case R.id.add_remind:
                if (brandIv == null || productDetail == null) {
                    return;
                }
                setRemindCollect(productDetail.favoriteFlag);
                break;
            //在线客服
            case R.id.ll_on_line_service:
                sendOnLineService(productDetail);
                break;
            case R.id.ll_company_name:
                // guanchong 商品详情，跳转店铺
                if (jgTrackBean != null) {
                    JGTrackTopLevelKt.jgTrackProductDetailBtnClick(getActivity(), "进店逛逛", jgTrackBean.getJgReferrer());
                }
                if (productDetail != null && productDetail.isThirdCompany == 1) {
                    String mUrl = "ybmpage://shopactivity?orgId=" + productDetail.orgId;
                    String mEntrance = "";
                    if (jgTrackBean != null && jgTrackBean.getEntrance() != null) {
                        mEntrance = jgTrackBean.getEntrance();
                    } else {
                        mEntrance = JGTrackManager.TrackProductDetail.TITLE;
                    }
                    HashMap<String, String> mParams = new HashMap<>();
                    mParams.put(JG_REFERRER, AppUtilKt.getFullClassName(this));
                    mParams.put(JG_REFERRER_TITLE, JGTrackManager.TrackProductDetail.TITLE);
                    mParams.put(JG_ENTRANCE, mEntrance);
                    mUrl = JGTrackTopLevelKt.splicingUrlWithParams(mUrl, mParams);
                    RoutersUtils.open(mUrl);
                }
                break;
            //纠错
            case R.id.tv_correction:
                Intent correctionIntent = new Intent(getNotNullActivity(), MainCorrectionActivity.class);
                correctionIntent.putExtra(IntentCanst.SKU_PRICE, fob);
                correctionIntent.putExtra(IntentCanst.SKU_ID, skuId + "");
                startActivity(correctionIntent);
                break;
            //资质认证和资质审核中
            case R.id.tv_audit_no_passed:
                if (jgTrackBean != null) {
                    JGTrackTopLevelKt.jgTrackProductDetailBtnClick(getActivity(), "资质认证", jgTrackBean.getJgReferrer());
                }
                ((BaseActivity) getNotNullActivity()).gotoAtivity(AptitudeActivity.class, null);
                break;
            case R.id.rt_self_shop_open://进入店铺
                if (jgTrackBean != null) {
                    JGTrackTopLevelKt.jgTrackProductDetailBtnClick(getActivity(), "店铺", jgTrackBean.getJgReferrer());
                }
                if (TextUtils.isEmpty(shopHomeUrl)) {
                    ToastUtils.showShort("找不到相关店铺地址");
                } else {
                    //如果为控销店铺，后端会返回一个h5链接，为了兼容老版本，客户端手动做下拼接
                    if (shopHomeUrl.startsWith("http")) {
                        RoutersUtils.open("ybmpage://commonh5activity?url=" + shopHomeUrl);
                    } else {
                        RoutersUtils.open(shopHomeUrl);
                    }

                }
                break;
            case R.id.detail_ll_self_shop://进入店铺
                if (productDetail != null) {
                    CommodityDetailReport.bottomBtnClickShop(requireActivity(), productDetail.shopCode);
                }
                if (jgTrackBean != null) {
                    JGTrackTopLevelKt.jgTrackProductDetailBtnClick(getActivity(), "店铺", jgTrackBean.getJgReferrer());
                }
                if (TextUtils.isEmpty(shopHomeUrl)) {
                    ToastUtils.showShort("找不到相关店铺地址");
                } else {
                    //如果为控销店铺，后端会返回一个h5链接，为了兼容老版本，客户端手动做下拼接
                    if (shopHomeUrl.startsWith("http")) {
                        RoutersUtils.open("ybmpage://commonh5activity?url=" + shopHomeUrl);
                    } else {
                        RoutersUtils.open(shopHomeUrl);
                    }

                }
                break;
            case R.id.add_spell_group://去拼团
                if (addSpellGroup == null || productDetail == null || actPtBean == null) {
                    return;
                }
                initShowSpellGroup(productDetail, actPtBean, true);
                if (view instanceof TextView) {
                    CommodityDetailReport.bottomBtnClickRight(requireActivity(), 1, ((TextView) view).getText().toString());
                }
                break;
            case R.id.tv_original_price:             // 折后价
            case R.id.tv_tax_discount_price:         // 秒杀折后价
            case R.id.tv_spell_group_original_price: // 拼团折后价
                showDiscountDialog();
                break;
            case R.id.ll_share:
                try {
                    if (getContext() instanceof ShareCallback) {
                        ((ShareCallback) getContext()).onShareCallback();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
        }
    }

    private ProductDetailPriceAfterDiscountBean mDiscountData;

    private void showDiscountDialog() {
        if (mDiscountPopWindow == null && mDiscountData != null) {
            mDiscountPopWindow = new ProductDiscountPopWindow(getNotNullActivity(), mDiscountData);
        }
        if (mDiscountPopWindow != null) {
            mDiscountPopWindow.show(getNotNullActivity().getWindow().getDecorView());
        }
    }

    /*
     * 在线客服
     * */
    private void sendOnLineService(ProductDetailBean detailBean) {
        if (jgTrackBean != null) {
            JGTrackTopLevelKt.jgTrackProductDetailBtnClick(getActivity(), "联系客服", jgTrackBean.getJgReferrer());
        }
        int isThirdCompany = detailBean != null ? detailBean.isThirdCompany : 0;
        RequestParams params = new RequestParams();
        params.put("isThirdCompany", isThirdCompany + "");

        HttpManager.getInstance().post(AppNetConfig.GET_IM_PACKURL, params, new BaseResponse<ImPackUrlBean>() {

            @Override
            public void onSuccess(String content, BaseBean<ImPackUrlBean> obj, ImPackUrlBean baseBean) {

                if (obj != null && obj.isSuccess()) {

                    if (baseBean != null) {
                        if (isThirdCompany == 1) {
                            RoutersUtils.open(RoutersUtils.getRouterPopCustomerServiceUrl(baseBean.IM_PACK_URL, detailBean.orgId, "", detailBean.companyName));
                        } else {
                            RoutersUtils.open(RoutersUtils.getRouterYbmDetailCustomerServiceUrl(baseBean.IM_PACK_URL));
                        }
                    }
                }

            }

        });

    }

    /**
     * 服务简介
     */
    public void setServiceAbstract() {
        OnProductDetailPopWindow();
        mCopWindow.show(plService);
    }

    /**
     * 优惠券
     */
    private void initShowCoupon() {
        String mEntrance = "";
        if (jgTrackBean != null && jgTrackBean.getEntrance() != null) {
            mEntrance = jgTrackBean.getEntrance();
        }

        JgTrackBean mJgTrackBean = new JgTrackBean(
                AppUtilKt.getFullClassName(CommodityFragment.this),
                JGTrackManager.TrackProductDetail.TITLE,
                JGTrackManager.TrackProductDetail.TITLE,
                JGTrackManager.TrackProductDetail.TITLE,
                JGTrackManager.TrackProductDetail.PAGE_ID,
                JGTrackManager.TrackProductDetail.TITLE,
                mEntrance,
                "",
                AppUtilKt.getFullClassName(CommodityFragment.this)
        );
        mPopWindowPromotion = new ShowPromotionPopWindowNew(getContext(), this);
        mPopWindowPromotion.setSkuId(skuId + "");
        mPopWindowPromotion.setMJgTrackBean(
                mJgTrackBean
        );
        //埋点
        RowsBean trackRowsBean = new RowsBean();
        trackRowsBean.setSkuId(skuId);
        if (mDetailBean != null) {
            trackRowsBean.shopCode = mDetailBean.rows.shopCode;
            trackRowsBean.shopName = mDetailBean.shopInfo.shopName;
        }
        mPopWindowPromotion.setRowsBean(trackRowsBean);
        mPopWindowPromotion.show(rlCoupon);
    }

    /*
     * 服务条款-底部弹出popWindow
     * */
    private void OnProductDetailPopWindow() {
        if (mCopWindow == null) {
            mCopWindow = new ProductDetailPopWindow2();
            mCopWindow.setNewData(productDetail != null ? productDetail.getPromiseList() : new ArrayList<>());
        }
    }

    /**
     * 拼团
     */
    private void initShowSpellGroup(ProductDetailBean productDetail, ActPtBean actPtBean, Boolean show) {
        if (productDetail == null || actPtBean == null || productDetail.isControlUnShowPrice())
            return;
//        if (mPopWindowSpellGroup == null) {
            productDetail.mJgTrackBean = jgTrackBean;
            mPopWindowSpellGroup = new ShowSpellGroupPopWindow(getNotNullActivity(), productDetail, actPtBean, mIsAssemble, mIsWholeSale);
            mPopWindowSpellGroup.mFlowData = mFlowData;
            mPopWindowSpellGroup.jgTrackBean = jgTrackBean;
            mPopWindowSpellGroup.mShopInfo = shopInfo;
//        }
        if (show) {
            mPopWindowSpellGroup.show(addSpellGroup);
            productDetailJGTrack(productDetail, shopInfo, true);
        }
    }

    /**
     * 拼团
     */
    private void initShowSpellGroup(ProductDetailBean productDetail, ActPtBean actPtBean, int type) {
        if (productDetail == null || actPtBean == null || productDetail.isControlUnShowPrice())
            return;
//        if (mPopWindowSpellGroup == null) {
            productDetail.mJgTrackBean = jgTrackBean;
            mPopWindowSpellGroup = new ShowSpellGroupPopWindow(getNotNullActivity(), productDetail, actPtBean, mIsAssemble, mIsWholeSale,
                    type);
            mPopWindowSpellGroup.mFlowData = mFlowData;
            mPopWindowSpellGroup.mShopInfo = shopInfo;
            mPopWindowSpellGroup.jgTrackBean = jgTrackBean;
            mPopWindowSpellGroup.jgExtendOuterBean = jgPdExtendOuterBean;
//        }
        mPopWindowSpellGroup.setSPELL_GROUP_RECOMMEND_TYPE(type);
        mPopWindowSpellGroup.show(addSpellGroup);
    }

    /**
     * 订阅提醒
     *
     * @param favoriteFlag 是否可以订阅 1.可订阅；0.不可订阅
     */
    private void setRemindCollect(int favoriteFlag) {

        boolean isRemindOld = favoriteFlag == 0;
        if (isRemindOld) {
            return;
        }
        int businessType = 1;
        requestData(businessType, productDetail.id);
    }


    /**
     * businessType 收藏类型 默认收藏不传，1: 表示有货提醒业务类型；2：降价提醒
     * merchantId   商品业务相关id
     * skuId        商户id
     * 服务端请求
     */
    private void requestData(final int businessType, final long skuId) {

        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(skuId));
        params.put("businessType", String.valueOf(businessType));

        HttpManager.getInstance().post(AppNetConfig.COLLECT, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                if (null != obj) {
                    if (obj.isSuccess()) {
                        if (addRemind != null) {
                            productDetail.favoriteStatus = 1;
                            productDetail.businessType = businessType;
                            handler.sendMessage(handler.obtainMessage(REMIND_COLLECT_CODE, productDetail.favoriteFlag = 0));
                        }
                        showRemindDialog();
                        //订阅成功发送event 事件
                        Event<Boolean> event = new Event<>(C.EventCode.SUBSCRIBE_SUCCEED, true);
                        EventBusUtil.sendEvent(event);
                    }
                }
            }
        });
    }

    private void showRemindDialog() {

        String str = "若该商品在45天内到货，药帮忙会提醒您！ 同时您可以在我的收藏夹查看您订阅过的所有商品";
        AlertDialogEx dialogEx = new AlertDialogEx(getNotNullActivity());
        dialogEx.setMessage(str).setCancelButton("我知道啦", (AlertDialogEx.OnClickListener) (dialog, button) -> dialog.dismiss()).setCancelable(false).setCanceledOnTouchOutside(false).setTitle("订阅成功").show();
    }


    /**
     * 降价通知
     */
    private void getAction(double fob, long id) {
        String price = UiUtils.transform(fob);
        Intent intent = new Intent(getNotNullActivity(), DepreciateInformActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString("id", id + "");
        bundle.putString("price", price);
        intent.putExtras(bundle);
        startActivityForResult(intent, DEPRECIATEINFORMCODE);
    }

    /**
     * 返回url
     */
    private String getRouterUrl(String companyName, String orgId) {
        return "ybmpage://popactivity?title=" + companyName + "&id=" + orgId;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //降价提醒
        if (requestCode == DEPRECIATEINFORMCODE && resultCode == RESULT_OK) {
            String businessType = data.getStringExtra("businessType");
            String favoriteStatus = data.getStringExtra("favoriteStatus");
            int type;
            int Status;
            if (addRemind != null) {
                try {
                    type = Integer.parseInt(businessType);
                } catch (Exception e) {
                    type = 0;
                }
                try {
                    Status = Integer.parseInt(favoriteStatus);
                } catch (Exception e) {
                    Status = 2;
                }
                productDetail.businessType = type;
                productDetail.favoriteStatus = Status;
                handler.sendMessage(handler.obtainMessage(REMIND_COLLECT_CODE, productDetail.favoriteFlag = 1));
            }
        }
    }

    /*
     * 清单
     * */
    private void initInventory(ProductDetailBean productDetail, CheckBox inventoryCb) {

//        if (detailLlInventory == null) {
//            return;
//        }
//        detailLlInventory.setOnClickListener(v -> checkInventory(productDetail, inventoryCb));
    }

    /*
     *清单
     * */
    private void checkInventory(ProductDetailBean productDetail, CheckBox inventoryCb) {
        final long id = productDetail.id;
        final String collect_net = inventoryCb.isChecked() ? AppNetConfig.CANCEL_ORDER_SELL_NO : AppNetConfig.ADD_ORDER_SELL_NO;
        final String collect_str = inventoryCb.isChecked() ? "取消成功" : "加入成功";

        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(id));

        HttpManager.getInstance().post(collect_net, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                if (null != obj) {
                    if (obj.isSuccess()) {

                        if (inventoryCb.isChecked()) {
                            productDetail.listStatus = 2;
                            inventoryCb.setChecked(false);
                            tvInventory.setText("加常购");
                            tvInventory.setActivated(false);
                            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, collect_str);
                        } else {
                            productDetail.listStatus = 1;
                            inventoryCb.setChecked(true);
                            tvInventory.setText("已加入");
                            tvInventory.setActivated(true);
                            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, collect_str);
                        }
                    }
                }
            }
        });
    }

    public void setFavoriteStatus(boolean isCheck) {
        if (isCheck) {
            productDetail.favoriteStatus = 2;
            productDetail.businessType = 0;
            handler.sendMessage(handler.obtainMessage(REMIND_COLLECT_CODE, productDetail.favoriteFlag = 1));
        } else {
            productDetail.favoriteStatus = 1;
        }
    }

    /**
     * 处理一审状态
     *
     * @param productDetail
     */
    private void handleAuditPassedVisible(ProductDetailBean productDetail) {
        if (productDetail != null) {
            if (AuditStatusSyncUtil.getInstance().isAuditFirstPassed()) {
                tvAuditPassedVisible.setVisibility(View.GONE);
            } else {
                tvAuditPassedVisible.setVisibility(View.VISIBLE);
                rlPriceLayout.setVisibility(View.GONE);
            }

            if (productDetail.licenseStatus == ConstantData.LICENSE_STATUS_UNCOMMITTED) {
                //资质未提交
                rlDetailParent.setVisibility(View.GONE);
                clAuditNoPassed.setVisibility(View.VISIBLE);
                tvAuditNoPassed.setText(getResources().getString(R.string.aptitude_authentication));
            } else if (productDetail.licenseStatus == ConstantData.LICENSE_STATUS_SY_AUDITING) {
                //资质审核中
                rlDetailParent.setVisibility(View.GONE);
                clAuditNoPassed.setVisibility(View.VISIBLE);
                tvAuditNoPassed.setText(getResources().getString(R.string.aptitude_auditing));
            } else {
                rlDetailParent.setVisibility(View.VISIBLE);
                clAuditNoPassed.setVisibility(View.GONE);
            }
        }
    }

    private int page = 1;
    private int limit = 10;

    /**
     * 获取推荐数据
     */
    private void getRecommendData() {
        RequestParams params = new RequestParams();
        params.put("offset", page + "");
        params.put("limit", limit + "");
        params.put("pageType", "4");//2 发现页为你推荐 3 首页为你推荐 4 商品详情页为你推荐
        params.put("csuId", SpUtil.getMerchantid());
        params.put("merchantId", SpUtil.getMerchantid());
        params.put("timestamp", System.currentTimeMillis() + "");
        // guanchong 为你推荐（商品详情）
        HttpManager.getInstance().post(AppNetConfig.HOME_RECOMMENDED_SKU, params, new BaseResponse<RefreshWrapperPagerBean<RowsBean>>() {
            @Override
            public void onSuccess(String content, BaseBean<RefreshWrapperPagerBean<RowsBean>> obj, RefreshWrapperPagerBean<RowsBean> data) {
                super.onSuccess(content, obj, data);
                if (obj != null && obj.isSuccess() && data != null && getContext() != null) {
                    if (data.getRows() == null || data.getRows().isEmpty()) {
                        if (page == 1) {
                            llModuleRecommend.setVisibility(View.GONE);
                        }
                        return;
                    }
                    FlowData flowData = new FlowData(data.getSpType(), data.getSpId(), data.getSpId(), data.getNsid(), "", null);
                    recommendAdapter.setFlowData(flowData);
                    page++;
                    recommendAdapter.removeFooterView(footerView);
                    recommendList.addAll(data.getRows());
                    // 请求并更新折后价
                    AdapterUtils.INSTANCE.getAfterDiscountPrice(data.getRows(), recommendAdapter);
                    recommendAdapter.notifyDataSetChanged();
                    recommendAdapter.notifyDataChangedAfterLoadMore(true);
                    if (limit < data.getRows().size()) {
                        recommendAdapter.addFooterView(View.inflate(getContext(), R.layout.not_loading, null));
                        loadMoreStatus = LOAD_MORE_STATUS_NO_MORE;
                    } else {
                        if (footerView == null) {
                            footerView = View.inflate(getContext(), R.layout.view_loadmore, null);
                        }
                        recommendAdapter.addFooterView(footerView);
                        loadMoreStatus = LOAD_MORE_STATUS_DEFAULT;
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }

            @Override
            public BaseBean json(String content, Type type) {
                return super.json(content, new TypeToken<BaseBean<RefreshWrapperPagerBean<RowsBean>>>() {
                }.getType());
            }
        });
    }


    @SuppressLint("HandlerLeak")
    private Handler handler = new Handler() {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            //更改收藏状态和订阅状态的ui 是否可订阅：1.可订阅；0.不可订阅
            if (msg.what == REMIND_COLLECT_CODE) {
                if (addRemind == null) {
                    return;
                }
                int favoriteFlag = (int) msg.obj;
                boolean isRemindOrDepreciate = (favoriteFlag == 1);
                if (mOnCheckListener != null) {
                    mOnCheckListener.onIsFavoriteStatus(productDetail.isFavoriteStatus());
                }
                addRemind.setText(isRemindOrDepreciate ? "到货提醒" : "已订阅");
                addRemind.setTextColor(isRemindOrDepreciate ? UiUtils.getColor(R.color.white) : UiUtils.getColor(R.color.white));
                addRemind.setBackgroundColor(isRemindOrDepreciate ? UiUtils.getColor(R.color.detail_tv_00B377) : UiUtils.getColor(R.color.color_A9AEB7));
                llRemind.setBackgroundColor(isRemindOrDepreciate ? UiUtils.getColor(R.color.detail_tv_00B377) : UiUtils.getColor(R.color.color_A9AEB7));
            }
        }
    };

    /*
     * 购物车数量
     * */
    private void getCouponShopNumber() {
        if (llDetailRl == null) {
            return;
        }
        int num = YBMAppLike.cartNum;
        if (num > 0) {
            String numStr = num > 99 ? "99+" : String.valueOf(num);
            llDetailTv.setText(numStr);
        }
        llDetailRl.setVisibility(num > 0 ? View.VISIBLE : View.INVISIBLE);
    }

    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.CART_NUM_CHANGED);
        intentFilter.addAction(IntentCanst.ACTION_CHANG_CAR_NUMBER);
        LocalBroadcastManager.getInstance(getNotNullActivity())
                .registerReceiver(mRefreshBroadcastReceiver, intentFilter);
    }

    private BroadcastReceiver mRefreshBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (IntentCanst.CART_NUM_CHANGED.equals(action)) {
                getCouponShopNumber();
            } else if (IntentCanst.ACTION_CHANG_CAR_NUMBER.equals(action)) {
                if (elEdit != null) {
                    elEdit.refreshData();
                }
            }
        }
    };

    @Override
    public void onDestroy() {
        if (mRefreshBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(getNotNullActivity().getApplication())
                    .unregisterReceiver(mRefreshBroadcastReceiver);
        }
        if (limitHandler != null) {
            limitHandler.removeCallbacks(runnable);
        }
        if (countDownTimer != null) {
            countDownTimer.cancel();
            countDownTimer = null;
        }
        if (limitTimePremiumCountDownTimer != null) {
            limitTimePremiumCountDownTimer.cancel();
            limitTimePremiumCountDownTimer = null;
        }
//        GSYVideoManager.releaseAllVideos();
        super.onDestroy();

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if(rlvCnAttr != null){
            rlvCnAttr.removeOnScrollListener(cnMedineScrollListener);
            if(rlvCnAttr.getViewTreeObserver().isAlive()){
                rlvCnAttr.getViewTreeObserver().removeOnGlobalLayoutListener(cnMedineGlobalListener);
            }
        }
        ButterKnife.unbind(this);
    }

    @Override
    public void toTop() {
    }

    @Override
    public void toBottom() {
    }

    public interface ProductDetailScrollListener {
        void onProductDetailScroll(int position);

    }

    /**
     * 是否是三方店铺
     *
     * @param productDetail
     * @return
     */
    public boolean isThirdCompany(ProductDetailBean productDetail) {
        return productDetail != null && productDetail.isThirdCompany != 1;
    }

    /**
     * 生成单位
     *
     * @param unit
     * @return
     */
    public String generateUnit(String unit) {
        return TextUtils.isEmpty(unit) ? "" : "/" + unit;
//        return TextUtils.isEmpty(unit) ? "元" : "元/" + unit;
    }

    /**
     * 生成单位
     *
     * @param unit
     * @return
     */
    public String generateUnit(String unit, boolean isStepPrice) {
        if (isStepPrice) {
            return TextUtils.isEmpty(unit) ? "起" : "起/" + unit;
        } else {
            return TextUtils.isEmpty(unit) ? "" : "/" + unit;
//            return TextUtils.isEmpty(unit) ? "元" : "元/" + unit;
        }
    }

    public boolean onBackPress() {
        return brandIv.onBackPress();
    }

    /**
     * 设置单价显示逻辑
     * 规则：
     * 1. 拼团商品：单价显示在第二行，如果第二行已有内容则放在首位并用竖线分隔，颜色为白色
     * 2. 其他商品：如果价格后方有其他内容（如折扣价），则单价放在其他内容后方；如果没有其他内容，则直接跟在价格后方
     * 3. 如果unitPrice没有值或为空字符串，则不显示
     */
    private void setUnitPriceDisplay(ProductDetailBean productDetail) {
        if (productDetail == null) return;

        // 获取单价信息
        String unitPrice = getUnitPriceText(productDetail);
        if (TextUtils.isEmpty(unitPrice)) {
            // 隐藏所有单价相关的视图
            hideAllUnitPriceViews();
            return;
        }

        // 判断是否为拼团商品
        if (mIsAssemble) {
            // 拼团商品：单价显示在第二行
            setSpellGroupUnitPrice(unitPrice);
        } else {
            // 其他商品：检查是否有折扣价等其他内容
            boolean hasDiscountPrice = tvOriginalPrice != null &&
                                       tvOriginalPrice.getVisibility() == View.VISIBLE &&
                                       !TextUtils.isEmpty(tvOriginalPrice.getText());

            if (hasDiscountPrice) {
                // 有折扣价等其他内容，在第二行显示单价（放在其他内容后方）
                setNormalUnitPriceInSecondLine(unitPrice);
            } else {
                // 无其他内容，在价格后方显示单价
                setUnitPriceAfterMainPrice(unitPrice);
            }
        }
    }

    /**
     * 获取单价文本
     */
    private String getUnitPriceText(ProductDetailBean productDetail) {
        if (productDetail == null || TextUtils.isEmpty(productDetail.unitPrice)) {
            return "";
        }
        return productDetail.unitPrice;
    }

    /**
     * 隐藏所有单价相关的视图
     */
    private void hideAllUnitPriceViews() {
        // 隐藏高毛利拼团布局的单价控件
        hideHighGrossUnitPrice();

        // 隐藏普通拼团布局的单价控件
        hideSimpleSpellGroupUnitPrice();

        // 隐藏限时加补拼团布局的单价控件
        View rootView = getView();
        if (rootView != null) {
            LinearLayout llLimitedTimeSecondLine = rootView.findViewById(R.id.ll_limited_time_second_line);
            if (llLimitedTimeSecondLine != null) {
                llLimitedTimeSecondLine.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 在价格后方显示单价（普通商品）
     * 注意：目前只实现拼团商品的单价显示，普通商品的单价显示需要后续完善
     */
    private void setUnitPriceAfterMainPrice(String unitPrice) {
        // 隐藏拼团布局的单价显示
        hideAllUnitPriceViews();

        // TODO: 在价格后方显示单价的逻辑需要根据具体的布局结构来实现
        // 目前暂时不实现，因为没有对应的控件
    }

    /**
     * 在第二行显示单价（普通商品）
     * 注意：目前只实现拼团商品的单价显示，普通商品的单价显示需要后续完善
     */
    private void setNormalUnitPriceInSecondLine(String unitPrice) {
        // 隐藏拼团布局的单价控件
        hideAllUnitPriceViews();

        // TODO: 在第二行显示单价的逻辑需要根据具体的布局结构来实现
        // 目前暂时不实现，因为没有对应的控件
    }

    /**
     * 设置拼团商品的单价显示
     */
    private void setSpellGroupUnitPrice(String unitPrice) {
        // 检查是否有第二行内容（建议零售价等）
        boolean hasGrossDes = tvSpellGroupGrossDes != null &&
                              tvSpellGroupGrossDes.getVisibility() == View.VISIBLE &&
                              !TextUtils.isEmpty(tvSpellGroupGrossDes.getText());

        // 根据显示的拼团布局类型选择对应的单价控件
        if (clSpellGroup2 != null && clSpellGroup2.getVisibility() == View.VISIBLE) {
            // 高毛利拼团布局：使用 tv_unit_price_bottom
            setHighGrossUnitPrice(unitPrice, hasGrossDes);

            // 隐藏普通拼团布局的单价控件
            hideSimpleSpellGroupUnitPrice();
        } else if (clSpellGroup1 != null && clSpellGroup1.getVisibility() == View.VISIBLE) {
            // 普通拼团布局：使用 tv_unit_price_simple
            setSimpleSpellGroupUnitPrice(unitPrice);

            // 隐藏高毛利拼团布局的单价控件
            hideHighGrossUnitPrice();
        }

        // 检查限时加补拼团布局
        if (llSpellGroupLimitedTimePremium != null && llSpellGroupLimitedTimePremium.getVisibility() == View.VISIBLE) {
            setLimitedTimeUnitPrice(unitPrice);
        }
    }

    /**
     * 设置特价/秒杀布局的单价显示（暂时注释，控件不存在）
     */
    private void setPromotionUnitPrice(String unitPrice) {
        // 暂时注释，因为相关控件可能不存在
        // if (tvPromotionUnitPrice != null) {
        //     tvPromotionUnitPrice.setVisibility(View.VISIBLE);
        //     tvPromotionUnitPrice.setText(unitPrice);
        //     tvPromotionUnitPrice.setTextColor(getResources().getColor(R.color.white));
        //     tvPromotionUnitPrice.setBackgroundColor(getResources().getColor(R.color.color_F5F5F5));
        // }

        // 检查是否有建议零售价
        // boolean hasSuggestPrice = rlSuggestedRetailPrice03 != null &&
        //                           rlSuggestedRetailPrice03.getVisibility() == View.VISIBLE;

        // if (llSecondLinePromotion != null) {
        //     llSecondLinePromotion.setVisibility(View.VISIBLE);
        // }

        // 设置分隔线
        // if (viewPromotionDivider != null) {
        //     if (hasSuggestPrice) {
        //         viewPromotionDivider.setVisibility(View.VISIBLE);
        //     } else {
        //         viewPromotionDivider.setVisibility(View.GONE);
        //     }
        // }
    }

    /**
     * 设置拼团未开始布局的单价显示（暂时注释，控件不存在）
     */
    private void setNoStartUnitPrice(String unitPrice) {
        // 暂时注释，因为相关控件可能不存在
        // if (tvNoStartUnitPrice != null) {
        //     tvNoStartUnitPrice.setVisibility(View.VISIBLE);
        //     tvNoStartUnitPrice.setText(unitPrice);
        //     tvNoStartUnitPrice.setTextColor(getResources().getColor(R.color.white));
        //     tvNoStartUnitPrice.setBackgroundColor(getResources().getColor(R.color.color_F5F5F5));
        // }

        // if (llNoStartUnitPriceLine != null) {
        //     llNoStartUnitPriceLine.setVisibility(View.VISIBLE);
        // }
    }



    /**
     * 隐藏所有单价控件（包括拼团、特价、秒杀等各种布局）
     */
    private void hideSpellGroupUnitPriceViews() {
        // 隐藏拼团布局的单价控件
        if (tvUnitPriceBottom != null) {
            tvUnitPriceBottom.setVisibility(View.GONE);
        }
        if (viewDivider1 != null) {
            viewDivider1.setVisibility(View.GONE);
        }
        if (tvUnitPriceSimple != null) {
            tvUnitPriceSimple.setVisibility(View.GONE);
        }

        // 隐藏其他特殊布局的单价控件（如果存在的话）
        // 注意：以下控件可能在当前版本中不存在，但保留以确保兼容性
        hideOtherLayoutUnitPriceViews();
    }

    /**
     * 隐藏其他布局的单价控件
     */
    private void hideOtherLayoutUnitPriceViews() {
        // 隐藏特价/秒杀布局的单价控件（暂时注释，控件可能不存在）
        // if (tvPromotionUnitPrice != null) {
        //     tvPromotionUnitPrice.setVisibility(View.GONE);
        // }
        // if (viewPromotionDivider != null) {
        //     viewPromotionDivider.setVisibility(View.GONE);
        // }
        // if (llSecondLinePromotion != null) {
        //     llSecondLinePromotion.setVisibility(View.GONE);
        // }

        // 隐藏拼团未开始布局的单价控件（暂时注释，控件可能不存在）
        // if (tvNoStartUnitPrice != null) {
        //     tvNoStartUnitPrice.setVisibility(View.GONE);
        // }
        // if (llNoStartUnitPriceLine != null) {
        //     llNoStartUnitPriceLine.setVisibility(View.GONE);
        // }

        // 隐藏限时加补拼团布局的单价控件（暂时注释，控件可能不存在）
        // if (tvLimitedTimeUnitPrice != null) {
        //     tvLimitedTimeUnitPrice.setVisibility(View.GONE);
        // }
        // if (viewLimitedTimeDivider != null) {
        //     viewLimitedTimeDivider.setVisibility(View.GONE);
        // }
    }

    /**
     * 设置高毛利拼团布局的单价显示
     */
    private void setHighGrossUnitPrice(String unitPrice, boolean hasGrossDes) {
        // 显示第二行容器
        View rootView = getView();
        if (rootView != null) {
            LinearLayout llSecondLine = rootView.findViewById(R.id.ll_second_line);
            if (llSecondLine != null) {
                llSecondLine.setVisibility(View.VISIBLE);
            }
        }

        // 设置单价
        if (tvUnitPriceBottom != null) {
            tvUnitPriceBottom.setVisibility(View.VISIBLE);
            tvUnitPriceBottom.setText(unitPrice);
            tvUnitPriceBottom.setTextColor(getResources().getColor(R.color.white));
        }

        // 设置分隔线和建议零售价
        if (hasGrossDes) {
            if (viewDivider1 != null) {
                viewDivider1.setVisibility(View.VISIBLE);
            }
            if (tvSpellGroupGrossDes != null) {
                tvSpellGroupGrossDes.setVisibility(View.VISIBLE);
            }
        } else {
            if (viewDivider1 != null) {
                viewDivider1.setVisibility(View.GONE);
            }
            if (tvSpellGroupGrossDes != null) {
                tvSpellGroupGrossDes.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 设置普通拼团布局的单价显示
     */
    private void setSimpleSpellGroupUnitPrice(String unitPrice) {
        // 显示第二行容器
        View rootView = getView();
        if (rootView != null) {
            LinearLayout llSecondLineSimple = rootView.findViewById(R.id.ll_second_line_simple);
            if (llSecondLineSimple != null) {
                llSecondLineSimple.setVisibility(View.VISIBLE);
            }

            // 设置分隔线（普通拼团布局中单价后面是拼团进度）
            View viewDividerSimple = rootView.findViewById(R.id.view_divider_simple);
            if (viewDividerSimple != null) {
                viewDividerSimple.setVisibility(View.VISIBLE);
            }
        }

        // 设置单价
        if (tvUnitPriceSimple != null) {
            tvUnitPriceSimple.setVisibility(View.VISIBLE);
            tvUnitPriceSimple.setText(unitPrice);
            tvUnitPriceSimple.setTextColor(getResources().getColor(R.color.white));
        }
    }

    /**
     * 隐藏高毛利拼团布局的单价控件
     */
    private void hideHighGrossUnitPrice() {
        View rootView = getView();
        if (rootView != null) {
            LinearLayout llSecondLine = rootView.findViewById(R.id.ll_second_line);
            if (llSecondLine != null) {
                llSecondLine.setVisibility(View.GONE);
            }
        }

        if (tvUnitPriceBottom != null) {
            tvUnitPriceBottom.setVisibility(View.GONE);
        }
        if (viewDivider1 != null) {
            viewDivider1.setVisibility(View.GONE);
        }
    }

    /**
     * 隐藏普通拼团布局的单价控件
     */
    private void hideSimpleSpellGroupUnitPrice() {
        View rootView = getView();
        if (rootView != null) {
            LinearLayout llSecondLineSimple = rootView.findViewById(R.id.ll_second_line_simple);
            if (llSecondLineSimple != null) {
                llSecondLineSimple.setVisibility(View.GONE);
            }
        }

        if (tvUnitPriceSimple != null) {
            tvUnitPriceSimple.setVisibility(View.GONE);
        }
    }

    /**
     * 设置限时加补拼团布局的单价显示
     */
    private void setLimitedTimeUnitPrice(String unitPrice) {
        View rootView = getView();
        if (rootView != null) {
            // 显示第二行容器
            LinearLayout llLimitedTimeSecondLine = rootView.findViewById(R.id.ll_limited_time_second_line);
            if (llLimitedTimeSecondLine != null) {
                llLimitedTimeSecondLine.setVisibility(View.VISIBLE);
            }

            // 设置单价
            TextView tvLimitedTimeUnitPrice = rootView.findViewById(R.id.tv_limited_time_unit_price);
            if (tvLimitedTimeUnitPrice != null) {
                tvLimitedTimeUnitPrice.setVisibility(View.VISIBLE);
                tvLimitedTimeUnitPrice.setText(unitPrice);
                tvLimitedTimeUnitPrice.setTextColor(getResources().getColor(R.color.white));
            }

            // 检查是否有限时内容
            boolean hasLimitContent = tvSpellGroupLimitContent != null &&
                                      tvSpellGroupLimitContent.getVisibility() == View.VISIBLE &&
                                      !TextUtils.isEmpty(tvSpellGroupLimitContent.getText());

            // 设置分隔线
            View viewLimitedTimeDivider = rootView.findViewById(R.id.view_limited_time_divider);
            if (viewLimitedTimeDivider != null) {
                if (hasLimitContent) {
                    viewLimitedTimeDivider.setVisibility(View.VISIBLE);
                } else {
                    viewLimitedTimeDivider.setVisibility(View.GONE);
                }
            }

            // 设置限时内容
            if (tvSpellGroupLimitContent != null) {
                if (hasLimitContent) {
                    tvSpellGroupLimitContent.setVisibility(View.VISIBLE);
                } else {
                    tvSpellGroupLimitContent.setVisibility(View.GONE);
                }
            }
        }
    }

}
